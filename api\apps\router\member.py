from fastapi import APIRouter, Request, Query, Depends, UploadFile, Form
from typing import Annotated

from apps.common import unified_resp
from apps.models.common import ObjectIdStr
from apps.services.member import MemberService
import apps.models.member as MemberModel
import apps.services.material as MaterialService


router = APIRouter(prefix="/member", tags=["会员管理"])


@router.post("", tags=["会员管理"])
@unified_resp
async def member_add(_: Request, member: MemberModel.MemberCreate):
    """新增会员"""
    member_srv = MemberService()
    return await member_srv.member_create(member)


@router.get("", tags=["会员管理"])
@unified_resp
async def member_list(_: Request, q: Annotated[MemberModel.MemberQuery, Query()]):
    """获取会员列表"""
    member_srv = MemberService()
    return await member_srv.member_list(q)


@router.get("/{member_id}", tags=["会员管理"])
@unified_resp
async def member_detail(_: Request, member_id: ObjectIdStr):
    """获取会员详情"""
    member_srv = MemberService()
    return await member_srv.member_detail(member_id)


@router.put("/{member_id}", tags=["会员管理"])
@unified_resp
async def member_update(
    _: Request,
    member_id: ObjectIdStr,
    member: MemberModel.MemberUpdate,
):
    """更新会员信息"""
    member_srv = MemberService()
    return await member_srv.member_update(member_id, member)


@router.delete("/{member_id}", tags=["会员管理"])
@unified_resp
async def member_delete(_: Request, member_id: ObjectIdStr):
    """删除会员"""
    member_srv = MemberService()
    return await member_srv.member_delete(member_id)


@router.post("/upload_photo", tags=["会员管理"], response_model=MemberModel.PhotoUploadOut)
@unified_resp
async def upload_photo(
    _: Request,
    file: UploadFile,
    member_id: str = Form(...),
) -> MemberModel.PhotoUploadOut:
    """上传会员照片"""
    # 使用现有的素材管理服务上传文件
    upload_result = await MaterialService.upload_material(file, cid=0)
    
    # 获取上传后的文件URL
    photo_url = upload_result.get("url", "")
    
    # 更新会员照片信息
    member_srv = MemberService()
    return await member_srv.upload_photo(member_id, photo_url)
