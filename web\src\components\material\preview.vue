<template>
  <div v-show="modelValue">
    <div v-if="type == 'image'">
      <el-image-viewer v-if="previewLists.length" :url-list="previewLists" hide-on-click-modal @close="handleClose" />
    </div>
    <div v-if="type == 'video'">
      <el-dialog v-model="visible" width="740px" :title="title" :before-close="handleClose">
        <video-player ref="playerRef" :src="url" width="100%" height="450px" @timeupdate="handleUpdate" />
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useAppStore from "@/stores/modules/app";
// import { getControllerData } from "@/api/device";

const { getImageUrl } = useAppStore();
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  url: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "image",
  },
  isShowHandle: {
    type: Boolean,
    default: false,
  },
  startTime: {
    type: String,
    default: "",
  },
  endTime: {
    type: String,
    default: "",
  },
  vehicleId: {
    type: String,
    default: "",
  },
});
const emit = defineEmits<{ (event: "update:modelValue", value: boolean): void }>();

const playerRef = shallowRef();

const visible = computed({
  get() {
    return props.modelValue;
  },

  set(value) {
    emit("update:modelValue", value);
  },
});

const handleClose = () => {
  emit("update:modelValue", false);
};

const handleUpdate = (e: any) => {
  const curTime = Math.ceil(new Date(props.startTime).getTime() / 1000 + e.target.currentTime).toString();

  if (controlData.value[curTime]) {
    curControlData.value.leftJoystickX = controlData.value[curTime].left_joystick_x;
    curControlData.value.leftJoystickY = controlData.value[curTime].left_joystick_y;
    curControlData.value.rightJoystickX = controlData.value[curTime].right_joystick_x;
    curControlData.value.rightJoystickY = controlData.value[curTime].right_joystick_y;
    curControlData.value.leftPedal = controlData.value[curTime].left_pedal;
    curControlData.value.rightPedal = controlData.value[curTime].right_pedal;
  }
};

const previewLists = ref<any[]>([]);

const curControlData = ref<any>({
  leftJoystickX: 0,
  leftJoystickY: 0,
  rightJoystickX: 0,
  rightJoystickY: 0,
  leftPedal: 0,
  rightPedal: 0,
});

const controlData = ref<any>({});

const getHandleData = async () => {

};

onMounted(() => {
  if (props.isShowHandle) {
    getHandleData();
  }
});

watch(
  () => props.modelValue,
  (value) => {
    if (value) {
      nextTick(() => {
        previewLists.value = [props.url];
        playerRef.value?.play();
      });
    } else {
      nextTick(() => {
        previewLists.value = [];
        playerRef.value?.pause();
      });
    }
  }
);
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 0 !important;
}
</style>
