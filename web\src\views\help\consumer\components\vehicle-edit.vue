<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :async="true"
      width="450px"
      @confirm="handleSubmit"
      @close="handleClose"
      :confirmButtonText="$t('stream.确定')"
      :cancelButtonText="$t('stream.取消')"
    >
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="重機番号" prop="vehicle_name">
          <el-input v-model="formData.vehicle_name" placeholder="例：ショベル001" clearable />
        </el-form-item>
        <!-- <el-form-item label="重機番型" prop="vehicle_type">
          <el-select v-model="formData.vehicle_type" placeholder="请选择车辆类型" clearable>
            <el-option
              v-for="item in vehicleTypeList"
              :key="item.flag"
              :label="item.name"
              :value="Number(item.flag)"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="重機側SN番号" prop="vehicle_sn">
          <el-input v-model="formData.vehicle_sn" placeholder="コントロールボックスSN番号を入力してください" clearable />
        </el-form-item>
        <el-form-item label="所在地" prop="vehicle_address">
          <el-input v-model="formData.vehicle_address" placeholder="所在地を入力してください" clearable />
        </el-form-item>
        <el-form-item label="機能付き" prop="smart_feature">
          <el-select
            v-model="formData.smart_feature"
            placeholder="付きオプション機能を選択してください"
            multiple
            clearable
          >
            <el-option
              v-for="item in smartFeatureList"
              :key="item.flag"
              :label="item.name"
              :value="Number(item.flag)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="取り付け日" prop="install_date">
          <el-date-picker
            v-model="formData.install_date"
            type="date"
            placeholder="取り付け日を選択してください"
            clearable
          />
        </el-form-item>
        <el-form-item label="引き渡し日" prop="delivery_date">
          <el-date-picker
            v-model="formData.delivery_date"
            type="date"
            placeholder="引き渡し日を選択してください"
            clearable
          />
        </el-form-item>
        <el-form-item label="保証終了日" prop="warranty_end_date">
          <el-date-picker
            v-model="formData.warranty_end_date"
            type="date"
            placeholder="保証終了日を選択してください"
            clearable
          />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import { guideList as guideListApi } from "@/api/guide-man";
import { projectAddVehicle, projectEditVehicle } from "@/api/project-man";
import { getUserList } from "../util";
import useMetadataStore from "@/stores/modules/metadata";

const props = defineProps({ id: String });
const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "編集" : "新規追加";
});

const formData: any = reactive({
  vehicle_id: "",
  vehicle_name: "",
  vehicle_type: 1,
  vehicle_sn: "",
  vehicle_address: "",
  smart_feature: [],
  install_date: null,
  delivery_date: null,
  warranty_end_date: null,
});

const formRules = reactive({
  vehicle_name: [
    {
      required: true,
      message: "例：ショベル001",
      trigger: ["blur"],
    },
  ],
});

const vehicleTypeList: any = ref([]);
const smartFeatureList: any = ref([]);
const metadataStore = useMetadataStore();

onMounted(async () => {
  await getVehicleTypeList();
});

const getVehicleTypeList = async () => {
  vehicleTypeList.value = await metadataStore.fetchMetadata("vehicle_type");
  smartFeatureList.value = await metadataStore.fetchMetadata("smart_feature");
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit"
    ? await projectEditVehicle({ id: props.id, ...formData })
    : await projectAddVehicle({ id: props.id, ...formData });
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (data: any) => {
  console.log(data);

  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
