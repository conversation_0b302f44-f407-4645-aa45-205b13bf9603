from typing import List, Optional
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, validator
from .common import PagingModel, ObjectIdStr
import re


class CardType(str, Enum):
    """会员卡类型枚举"""
    YEAR_CARD = "年卡"
    COUNT_CARD = "次卡"


class MemberCreate(BaseModel):
    """会员创建模型"""
    name: str = Field(..., min_length=1, max_length=50, description="姓名")
    phone: str = Field(..., min_length=11, max_length=11, description="手机号")
    card_type: CardType = Field(..., description="会员卡类型")
    expire_time: Optional[datetime] = Field(None, description="会员到期时间")
    coin_balance: float = Field(default=0, ge=0, description="游戏币余额")
    photo: Optional[str] = Field(None, description="会员照片URL")

    @validator('phone')
    def validate_phone(cls, v):
        """验证手机号格式"""
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v

    @validator('expire_time')
    def validate_expire_time(cls, v, values):
        """验证到期时间"""
        if v and v <= datetime.now():
            raise ValueError('到期时间不能早于当前时间')
        return v


class MemberUpdate(BaseModel):
    """会员更新模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="姓名")
    phone: Optional[str] = Field(None, min_length=11, max_length=11, description="手机号")
    card_type: Optional[CardType] = Field(None, description="会员卡类型")
    expire_time: Optional[datetime] = Field(None, description="会员到期时间")
    coin_balance: Optional[float] = Field(None, ge=0, description="游戏币余额")
    photo: Optional[str] = Field(None, description="会员照片URL")

    @validator('phone')
    def validate_phone(cls, v):
        """验证手机号格式"""
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v

    @validator('expire_time')
    def validate_expire_time(cls, v):
        """验证到期时间"""
        if v and v <= datetime.now():
            raise ValueError('到期时间不能早于当前时间')
        return v


class MemberQuery(PagingModel):
    """会员查询模型"""
    keyword: Optional[str] = Field(None, description="搜索关键词（姓名或手机号）")
    card_type: Optional[CardType] = Field(None, description="会员卡类型筛选")
    is_expired: Optional[bool] = Field(None, description="是否过期筛选")


class MemberOut(BaseModel):
    """会员输出模型"""
    id: ObjectIdStr = Field(..., alias="_id")
    name: str = Field(..., description="姓名")
    phone: str = Field(..., description="手机号")
    card_type: CardType = Field(..., description="会员卡类型")
    expire_time: Optional[datetime] = Field(None, description="会员到期时间")
    coin_balance: float = Field(..., description="游戏币余额")
    photo: Optional[str] = Field(None, description="会员照片URL")
    is_expired: bool = Field(..., description="是否已过期")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        populate_by_name = True


class MemberPhotoUpload(BaseModel):
    """会员照片上传模型"""
    member_id: ObjectIdStr = Field(..., description="会员ID")
    photo_url: str = Field(..., description="照片URL")
