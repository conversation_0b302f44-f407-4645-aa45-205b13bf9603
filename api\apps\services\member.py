import json
from typing import Dict, Any, Optional, List
from datetime import datetime

import arrow
from bson import ObjectId
from pymongo.errors import DuplicateKeyError
from motor.motor_asyncio import AsyncIOMotorCollection

from apps.db import MongoDB
from apps.common import HttpResp, AppException
from apps.utils.patterns import singleton
import apps.models.member as MemberModel


@singleton
class MemberService:
    """会员管理服务"""

    def __init__(self, _=None):
        self.collection: AsyncIOMotorCollection = MongoDB.get_collection("members")

    async def member_create(self, member: MemberModel.MemberCreate) -> Dict[str, Any]:
        """创建会员"""
        # 检查手机号是否已存在
        existing_member = await self.collection.find_one({"phone": member.phone})
        if existing_member:
            raise AppException(HttpResp.FAILED, "手机号已存在")

        member_data = member.model_dump()
        member_data.update({
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        })

        try:
            result = await self.collection.insert_one(member_data)
            assert result.inserted_id, "Member insert failed"
            
            # 返回创建的会员信息
            created_member = await self.collection.find_one({"_id": result.inserted_id})
            return self._format_member_output(created_member)
        except Exception as e:
            raise AppException(HttpResp.FAILED, f"创建会员失败: {str(e)}")

    async def member_list(self, query: MemberModel.MemberQuery) -> Dict[str, Any]:
        """获取会员列表"""
        # 构建查询条件
        filter_dict = {}
        
        # 关键词搜索（姓名或手机号）
        if query.keyword:
            filter_dict["$or"] = [
                {"name": {"$regex": query.keyword, "$options": "i"}},
                {"phone": {"$regex": query.keyword, "$options": "i"}}
            ]
        
        # 会员卡类型筛选
        if query.card_type:
            filter_dict["card_type"] = query.card_type.value

        # 计算总数
        total = await self.collection.count_documents(filter_dict)
        
        # 分页查询
        skip = (query.page_no - 1) * query.page_size
        cursor = self.collection.find(filter_dict).sort("create_time", -1).skip(skip).limit(query.page_size)
        
        members = []
        async for member in cursor:
            members.append(self._format_member_output(member))

        # 如果有过期筛选条件，在内存中进行二次筛选
        if query.is_expired is not None:
            members = [m for m in members if m["is_expired"] == query.is_expired]
            total = len(members)

        return {
            "lists": members,
            "count": total,
            "page_no": query.page_no,
            "page_size": query.page_size
        }

    async def member_detail(self, member_id: ObjectId) -> Dict[str, Any]:
        """获取会员详情"""
        member = await self.collection.find_one({"_id": member_id})
        if not member:
            raise AppException(HttpResp.FAILED, "会员不存在")
        
        return self._format_member_output(member)

    async def member_update(self, member_id: ObjectId, member: MemberModel.MemberUpdate) -> Dict[str, Any]:
        """更新会员信息"""
        # 检查会员是否存在
        existing_member = await self.collection.find_one({"_id": member_id})
        if not existing_member:
            raise AppException(HttpResp.FAILED, "会员不存在")

        # 如果更新手机号，检查是否与其他会员重复
        if member.phone and member.phone != existing_member.get("phone"):
            phone_exists = await self.collection.find_one({
                "phone": member.phone,
                "_id": {"$ne": member_id}
            })
            if phone_exists:
                raise AppException(HttpResp.FAILED, "手机号已存在")

        # 构建更新数据
        update_data = {}
        for field, value in member.model_dump(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        update_data["update_time"] = arrow.utcnow().datetime

        try:
            result = await self.collection.update_one(
                {"_id": member_id},
                {"$set": update_data}
            )
            
            if result.modified_count == 0:
                raise AppException(HttpResp.FAILED, "更新失败")
            
            # 返回更新后的会员信息
            updated_member = await self.collection.find_one({"_id": member_id})
            return self._format_member_output(updated_member)
        except Exception as e:
            raise AppException(HttpResp.FAILED, f"更新会员失败: {str(e)}")

    async def member_delete(self, member_id: ObjectId) -> Dict[str, Any]:
        """删除会员"""
        result = await self.collection.delete_one({"_id": member_id})
        
        if result.deleted_count == 0:
            raise AppException(HttpResp.FAILED, "会员不存在或删除失败")
        
        return {"msg": "删除成功"}

    async def upload_photo(self, member_id: ObjectId, photo_url: str) -> Dict[str, Any]:
        """上传会员照片"""
        # 检查会员是否存在
        member = await self.collection.find_one({"_id": member_id})
        if not member:
            raise AppException(HttpResp.FAILED, "会员不存在")

        # 更新照片URL
        result = await self.collection.update_one(
            {"_id": member_id},
            {"$set": {"photo": photo_url, "update_time": arrow.utcnow().datetime}}
        )
        
        if result.modified_count == 0:
            raise AppException(HttpResp.FAILED, "照片上传失败")
        
        return {"photo_url": photo_url, "msg": "照片上传成功"}

    def _format_member_output(self, member: Dict[str, Any]) -> Dict[str, Any]:
        """格式化会员输出数据"""
        if not member:
            return {}
        
        # 判断是否过期
        is_expired = False
        if member.get("expire_time"):
            is_expired = member["expire_time"] < datetime.now()
        
        member["is_expired"] = is_expired
        return member
