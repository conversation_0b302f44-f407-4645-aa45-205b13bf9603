from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from apps.config import get_settings


__all__ = ["MongoUtil", "MongoDB"]


class MongoUtil:
    """MongoDB工具类"""

    __uri = "mongodb://{}:{}@{}:{}/{}?authSource=admin".format(
        get_settings().mongo_username,
        get_settings().mongo_password,
        get_settings().mongo_host,
        get_settings().mongo_port,
        get_settings().mongo_db_name,
    )
    connect: AsyncIOMotorClient = AsyncIOMotorClient(__uri, maxPoolSize=50)
    db: AsyncIOMotorDatabase = connect[get_settings().mongo_db_name]

    @classmethod
    def get_db(cls):
        """获取数据库实例"""
        return cls.db

    @classmethod
    def get_connect(cls):
        """获取连接实例"""
        return cls.connect

    @classmethod
    def close(cls):
        """关闭连接"""
        cls.connect.close()

    @classmethod
    async def start_transaction(cls):
        """开始事务"""
        session = await cls.connect.start_session()
        session.start_transaction()
        return session

    @classmethod
    async def commit_transaction(cls, session):
        """提交事务"""
        session.commit_transaction()

    @classmethod
    async def abort_transaction(cls, session):
        """回滚事务"""
        session.abort_transaction()

    @classmethod
    async def end_session(cls, session):
        """结束会话"""
        session.end_session()

    @classmethod
    async def init_index(cls):
        """索引初始化"""
        db = cls.get_db()

        # 用户相关
        await db["users"].create_index("username", unique=True)
        await db["users"].create_index("email", unique=True)
        await db["roles"].create_index("name", unique=True)

        # 数据相关
        await db["meta_data"].create_index("key", unique=True)
        await db["video_data"].create_index("path", unique=True)

        # 车辆相关
        await db["vehicles"].create_index([("vehicle_name", 1), ("organization", 1)], unique=True)
        await db["vehicles_parameter"].create_index("vehicle_id", unique=True)
        await db["vehicles_android_parameter"].create_index("vehicle_id", unique=True)

        # 操作台相关
        await db["op_consoles"].create_index([("name", 1), ("organization", 1)], unique=True)
        await db["op_consoles_parameter"].create_index([("opc_id", 1), ("vehicle_id", 1)], unique=True)


MongoDB = MongoUtil.get_db()
