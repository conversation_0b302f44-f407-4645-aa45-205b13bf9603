from typing import Optional, Text
from pydantic import BaseModel

from pydantic import BaseModel, Field
from datetime import datetime
from .common import PagingModel, ObjectIdStr


class ProjectCreate(BaseModel):
    prj_name: str


class ProjectQuery(PagingModel):
    prj_name: Optional[str] = None


class ProjectOut(ProjectCreate):
    id: ObjectIdStr = Field(..., alias="_id")
    # 0：未开始 1：已完成  2：生产中 3：测试中 4:已发货 5：部署中 6:验收中 99：有工单
    prj_status: int = 0
    prj_desc: Optional[str] = None
    create_time: datetime


class ProjectDetailOut(ProjectOut):
    prj_desc: Optional[str] = None
    prj_position: Optional[list] = []
    prj_address: Optional[str] = None
    prj_progress: list
    first_party: Optional[str] = None
    is_public_network: bool
    is_oms: bool
    devices_num: int
    work_order_num: int
    completion_rate: int
    installer: list[dict]
    salesperson: list[dict]
    vehicle: list[dict]
    console: list[dict]
    work_order: list[dict]
    update_time: datetime
    devices_info: Optional[str] = None
    installer_info: Optional[str] = None


class ProjectVehicleOut(BaseModel):
    id: ObjectIdStr = Field(..., alias="_id")
    vehicle_name: str


class ProjectBaseUpdate(BaseModel):
    prj_name: Optional[str] = None
    prj_desc: Optional[str] = None
    salesperson: Optional[list] = []
    first_party: Optional[str] = None
    is_public_network: Optional[bool] = None
    is_oms: Optional[bool] = None


class ProjectMapUpdate(BaseModel):
    prj_position: Optional[list] = []
    prj_address: Optional[str] = None


class ProjectProgressUpdate(BaseModel):
    content: str
    hollow: Optional[bool] = False
    progress_type: Optional[str] = "primary"
    is_change_status: Optional[bool] = False
    change_status: Optional[int] = None


class ProjectVehicleAdd(BaseModel):
    vehicle_id: Optional[str] = None
    vehicle_name: str
    vehicle_type: int
    vehicle_sn: Optional[str] = None
    installer: Optional[list] = []
    guide_id: Optional[str] = None
    oms_id: Optional[str] = None
    smart_feature: Optional[list] = []


class ProjectConsoleAdd(BaseModel):
    console_name: str
    console_type: int
    console_sn: Optional[str] = None
