<template>
  <div class="vehicle-card">
    <div class="content ml-2 pt-1">
      <div class="base-item flex flex-row">
        <div class="label">お客様名:</div>
        <div class="value">{{ customer_name || "--" }}</div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">担当者名:</div>
        <div class="value">{{ person_name || "--" }}</div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">連絡先:</div>
        <div class="value">{{ contact_info || "--" }}</div>
      </div>
      <!-- <div class="base-item flex flex-row">
        <div class="label">重機側情報:</div>
        <div class="value">{{ devices_info || "--" }}</div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  prj_name: {
    type: String,
    default: "",
  },
  prj_desc: {
    type: String,
    default: "",
  },
  prj_status: {
    type: Number,
    default: 1,
  },
  prj_address: {
    type: String,
    default: "",
  },
  devices_info: {
    type: String,
    default: "",
  },
  customer_name: {
    type: String,
    default: "",
  },
  contact_info: {
    type: String,
    default: "",
  },
  person_name: {
    type: String,
    default: "",
  },
  create_time: {
    type: String,
    default: "",
  },
});

onMounted(async () => {});
</script>
<style scoped lang="scss">
.base-item {
  min-height: 30px;
  line-height: 30px;
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 14px;
  .label {
    min-width: 100px;
    color: #222831;
  }
  .value {
    color: #393e46;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
