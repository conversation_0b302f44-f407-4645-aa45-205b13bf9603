# Created by .ignore support plugin (hsz.mobi)
### Python template
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.manifest
*.spec
*.cover
*.py,cover
*.mo
*.pot
logs
*.log
*.sage.py
*.stackdump
__pypackages__/
cython_debug/
.roo

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

api/lib/
api/lib64/
api/parts/
api/sdist/
api/var/
api/wheels/
api/pip-wheel-metadata/
api/share/python-wheels/
api/pip-log.txt
api/pip-delete-this-directory.txt
api/htmlcov/
api/.tox/
api/.nox/
api/.coverage
api/.coverage.*
api/.cache
api/nosetests.xml
api/coverage.xml
api/.hypothesis/
api/.pytest_cache/
api/cover/
api/local_settings.py
api/db.sqlite3
api/db.sqlite3-journal
api/instance/
api/.webassets-cache
api/.scrapy
api/docs/_build/
api/.pybuilder/
api/target/
api/.ipynb_checkpoints
api/profile_default/
api/ipython_config.py
api/celerybeat-schedule
api/celerybeat.pid
# api/.env
# api/.env.test
# api/env/
# api/ENV/
# api/env.bak/
api/.venv
api/venv/
api/venv.bak/
api/.spyderproject
api/.spyproject
api/.ropeproject
api//site
api/.mypy_cache/
api/.dmypy.json
api/dmypy.json
api/.pyre/
api/.pytype/
api/Thumbs.db
api/Thumbs.db:encryptable
api/ehthumbs.db
api/ehthumbs_vista.db
api/*.cab
api/*.msi
api/*.msix
api/*.msm
api/*.msp
api/*.lnk
api/.fuse_hidden*
api/.directory
api/.Trash-*
api/.nfs*
api/.DS_Store
api/.AppleDouble
api/.LSOverride
api/Icon
api/.DocumentRevisions-V100
api/.fseventsd
api/.Spotlight-V100
api/.TemporaryItems
api/.Trashes
api/.VolumeIcon.icns
api/.com.apple.timemachine.donotpresent
api/.AppleDB
api/.AppleDesktop
api/.apdisk
api/temp/
api/static/

web/npm-debug.log*
web/yarn-debug.log*
web/yarn-error.log*
web/pnpm-debug.log*
web/lerna-debug.log*
web/node_modules
web/.DS_Store
web/dist
web/dist-ssr
web/coverage
web/*.local

# unplugin-auto-import
web/auto-imports.d.ts
web/components.d.ts
web/.eslintrc-auto-import.json
web/stats.html

# Editor directories and files
web/.idea
web/*.suo
web/*.ntvs*
web/*.njsproj
web/*.sln
web/*.sw?