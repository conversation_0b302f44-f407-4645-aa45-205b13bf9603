from typing import Dict, Any, Optional, List


import arrow
from bson import ObjectId
from pymongo.errors import DuplicateKeyError

from apps.db import MongoDB, RedisDB
from apps.common import HttpResp, AppException
import apps.models.user as UserModel
import apps.models.redis_key as RedisKey
import apps.models.opconsole as OPCModel
import apps.models.device as DeviceModel
import apps.models.vehicle as VModel
import apps.services.vehicles as VehicleService
import apps.services.role as RoleService
from apps.services.permissions import RoleResourceFlag


COLL = MongoDB.get_collection("op_consoles")


async def find_by_id(opc_id: ObjectId):
    """根据操作台id查询操作台信息"""
    return await COLL.find_one({"_id": opc_id})


async def find_by_device_id(device_id: str):
    """根据设备ID查询操作台信息"""
    return await COLL.find_one({"devices.id": device_id})


async def add(data: OPCModel.Add) -> Dict[str, Any]:
    """操作台添加"""
    opc_data = data.model_dump()
    opc_data.update(
        {
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )

    insert_data = await COLL.insert_one(opc_data)
    assert insert_data.inserted_id, "op_console insert failed."
    return {}


async def delete(opc_id: ObjectId) -> Dict[str, Any]:
    """操作台删除"""
    result = await COLL.delete_one({"_id": opc_id})
    assert result.deleted_count == 1, "op_console delete failed."
    return {}


async def update(opc_id: ObjectId, data: OPCModel.Update) -> Dict[str, Any]:
    """操作台更新"""
    update_data: dict = data.model_dump(exclude_none=True, exclude_defaults=True)

    update_data.update(
        {
            "update_time": arrow.utcnow().datetime,
            "vehicle_type": data.vehicle_type,
        }
    )
    result = await COLL.find_one_and_update({"_id": opc_id}, {"$set": update_data})
    assert result is not None, "op_console update failed."
    return {}


async def query(q: OPCModel.Query, user: UserModel.CacheInfo) -> Dict[str, Any]:
    """操作台列表"""
    skip = (q.page_no - 1) * q.page_size
    filter_d: Dict[str, Any] = {}

    if q.name:
        filter_d.update({"name": {"$regex": q.name}})
    if q.device_id:
        filter_d.update({"devices.id": q.device_id})

    # 根据用户有权限车辆列表，如果是超级管理员则不过滤
    if not user.is_super_admin:
        if not user.role_ids:
            return {"count": 0, "lists": []}
        opc_ids = await RoleService.get_opcs(user.role_ids)
        if not opc_ids:
            return {"count": 0, "lists": []}
        filter_d.update({"_id": {"$in": opc_ids}})

    count = await COLL.count_documents(filter_d)
    results = COLL.find(filter_d).skip(skip).limit(q.page_size)

    res = []
    async for item in results:
        opc_obj = OPCModel.Detail(**item)
        res.append(opc_obj.model_dump())

    return {"count": count, "lists": res}


async def get_opc_id_for_device(device_id: str, sys_info: Optional[Dict] = None):
    """根据设备 ID 获取操作台 ID"""
    odkey = RedisKey.OPCDevice(device_id)
    if sys_info:
        await RedisDB.hmset(odkey.status, sys_info)

    unbound = await RedisDB.get(odkey.unbound)
    if unbound:
        raise AppException(HttpResp.UNTRUSTED_DEVICE)

    opc_info = await find_by_device_id(odkey.id)
    if opc_info is None:
        # 未注册设备, 加入未注册设备列表
        await RedisDB.set(odkey.unbound, 1, timeout=5)
        await RedisDB.zadd(odkey.un_register_set, {odkey.id: int(arrow.utcnow().timestamp())})
        raise AppException(HttpResp.UNTRUSTED_DEVICE)

    # 已注册设备返回操作台ID
    return {
        "opc_id": opc_info["_id"],
        "opc_name": opc_info["name"],
        "opc_vehicle_type": opc_info.get("vehicle_type", []),
    }


async def unregister_device_list():
    """获取未注册设备列表"""
    unregister_list = await RedisDB.zget(RedisKey.OPCDevice.un_register_set)
    res_data = []
    for device_id in unregister_list:
        odkey = RedisKey.OPCDevice(device_id)
        device_info = await RedisDB.hgetall(odkey.status)
        if device_info:
            device_info["device_id"] = device_id
            res_data.append(DeviceModel.UnregisteredDeviceInfo(**device_info))
        else:
            await RedisDB.zrem(odkey.un_register_set, device_id)
    return {"count": len(res_data), "lists": res_data}


async def bind_device(opc_id: ObjectId, device_id: str):
    """绑定设备, 将设备 ID 添加到操作台的devices字段列表中"""
    index_ = await RedisDB.zkindex(RedisKey.OPCDevice.un_register_set, device_id)
    if index_ is None:
        raise AppException(HttpResp.DEVICE_NOT_FOUND)

    # get device status
    odkey = RedisKey.OPCDevice(device_id)
    device_status = await RedisDB.hgetall(odkey.status)
    if not device_status:
        raise AppException(HttpResp.DEVICE_NOT_FOUND)

    # add device to opc
    try:
        _ = await COLL.update_one(
            {"_id": opc_id, "devices.id": {"$ne": device_id}},
            {
                "$push": {
                    "devices": {
                        "id": device_id,
                        "name": device_status.get("device_name", ""),
                        "create_time": arrow.utcnow().datetime,
                    }
                }
            },
        )
    except DuplicateKeyError:
        raise AppException(HttpResp.DEVICE_ID_EXISTS)
    await RedisDB.zrem(RedisKey.OPCDevice.un_register_set, device_id)
    await RedisDB.delete(odkey.unbound)
    return {}


async def update_metadata(opc_id: str, data: Dict):
    """更新操作台元数据"""
    pass


async def filter_available_vehicle_for_type(
    vehicle_ids: List[ObjectId],
    vehicle_type: List[int],
) -> List[ObjectId]:
    """过滤出符合可操作的车辆类型的车辆ID"""
    results = VehicleService.COLL.find(
        {
            "_id": {"$in": vehicle_ids},
            "vehicle_type": {"$in": vehicle_type},
        },
        {"_id": 1},
    )
    data = []
    async for item in results:

        data.append(item["_id"])
    return data


async def find_available_vehicles(opc_id: ObjectId, user: UserModel.CacheInfo):
    """根据用户可以驾驶的车辆信息"""
    opc_info = await find_by_id(opc_id)
    assert opc_info is not None
    opc = OPCModel.Detail(**opc_info)

    if user.is_super_admin:
        vehicle_list = VehicleService.COLL.find({}, {"_id": 1})
        have_permission_ids = [item["_id"] async for item in vehicle_list]
    else:
        have_permission_ids = await RoleService.get_vehicles(user.role_ids, RoleResourceFlag.OPERATOR)

    # 过滤出符合可操作的车辆类型的车辆ID
    available_ids = await filter_available_vehicle_for_type(have_permission_ids, opc.vehicle_type)
    return available_ids


async def get_vehicles_android_params(vehicls_ids: List[ObjectId], opc_id: ObjectId):
    """根据操作台和用户获取可以驾驶的车辆信息"""
    vehicle_android_params = []
    for v_id in vehicls_ids:
        srv = VehicleService.Android(v_id)
        params = await srv.get_full_params()
        if params is None:
            continue
        if params["locked_opc"] is None:
            params["is_lock"] = VModel.LockStatus.UNLOCKED
        else:
            if str(opc_id) == params["locked_opc"]:
                params["is_lock"] = VModel.LockStatus.MY_LOCKED.value
            else:
                params["is_lock"] = VModel.LockStatus.OTHER_LOCKED.value

        vehicle_android_params.append(params)
    return vehicle_android_params


async def lock_vehicle(opc_id: ObjectId, vehicle_id: ObjectId, user: UserModel.CacheInfo):
    """操作台锁定车辆"""
    vkey = RedisKey.Vehicle(vehicle_id)
    okey = RedisKey.OPC(opc_id)

    # 检查车辆是否在操作台可操作车辆列表中
    available_vehicles = await find_available_vehicles(opc_id, user)
    if vehicle_id not in available_vehicles:
        raise AppException(HttpResp.NO_PERMISSION)

    # 锁车
    is_locked = await RedisDB.setnx(vkey.locked_op_console, str(opc_id))
    if not is_locked:
        locked_opc_id = await RedisDB.get(vkey.locked_op_console)
        if str(opc_id) != locked_opc_id:
            raise AppException(HttpResp.VEHICLE_LOCKED_BY_OTHER)

    # 添加锁定车辆进列表
    await RedisDB.sset(okey.locked_vehicles, str(vehicle_id))
    return {"msg": "lock success."}


async def unlock_vehicle(opc_id: ObjectId, vehicle_id: ObjectId):
    """操作台解锁车辆"""
    vkey = RedisKey.Vehicle(vehicle_id)
    okey = RedisKey.OPC(opc_id)

    locked_opc_id = await RedisDB.get(vkey.locked_op_console)
    if locked_opc_id != str(opc_id):
        raise AppException(HttpResp.NO_PERMISSION)

    await RedisDB.delete(vkey.locked_op_console)
    await RedisDB.srem(okey.locked_vehicles, str(vehicle_id))
    return {"msg": "unlock success."}


async def get_locked_vehicles(opc_id: ObjectId, user_id=None):
    """获取操作台锁定的车辆列表"""
    okey = RedisKey.OPC(opc_id)
    vehicle_ids = await RedisDB.sget(okey.locked_vehicles)
    return vehicle_ids
