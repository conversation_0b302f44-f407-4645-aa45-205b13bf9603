from enum import Enum
from typing import List, Union, Optional, Annotated, Dict, Any

from pydantic import BaseModel

from .ros import Node, NodeCrontrol, UpdateParams
from .vehicle import GMSLCamera, IPCamera, CAN
from .data_man import RecordVideoTask, RecordMqttTask


class DeviceDataSyncCode(Enum):
    synced = 0
    nosync = 1


class CommonCmd(Enum):
    ping = "ping"
    pong = "pong"


class DeviceRunData(BaseModel):
    ros_nodes: Optional[List[Node]] = []
    gmsl_cameras: Optional[List[GMSLCamera]] = []
    ip_cameras: Optional[List[IPCamera]] = []
    eth_can: Optional[List[CAN]] = []


class VehicleCmd(Enum):
    sync_check = "sync.check"
    sync_data = "sync.data"
    create_node = "ros.create_node"
    control_node = "ros.control_node"
    update_node_param = "ros.update_node_param"
    set_logtrack_node = "ros.set_logtrack_node"
    set_ipc = "ros.set_ipc"
    set_gmsl = "sys.set_gmsl"
    set_can = "sys.set_can"


class MsgModel(BaseModel):
    id: str = ""
    room_id: Optional[str] = None
    user: Optional[str] = None
    cmd: str
    data: Any


class PingPongModel(MsgModel):
    cmd: Annotated[str, CommonCmd]


class DataMangeMsg(MsgModel):
    cmd: Union[Annotated[str, CommonCmd], str]
    data: Union[
        List[RecordMqttTask],  # mqtt录制任务
        List[RecordVideoTask],  # 视频录制任务
        Dict,
    ] = {}


class VehicleMsg(MsgModel):
    cmd: Union[Annotated[str, VehicleCmd], Annotated[str, CommonCmd]]
    data: Union[
        Node,  # ros节点
        NodeCrontrol,  # 节点控制
        UpdateParams,  # ros节点更新参数
        DeviceRunData,  # 设备同步数据
        Dict,  #
    ] = {}


class OPCCmd(Enum):
    status_info = "status.opc.info"
    update_params = "service.opc.update_params"


class OPCMsg(MsgModel):
    cmd: Union[Annotated[str, OPCCmd], Annotated[str, CommonCmd]]
    data: Dict = {}


class UserMsg(MsgModel):
    data: Dict = {}
