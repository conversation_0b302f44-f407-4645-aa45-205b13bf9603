from typing import Optional
import logging

from pydantic import ValidationError
from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException
from redis.exceptions import ConnectionError as RedisConnectionError

from .http_base import HttpCode, HttpResp


__all__ = ["AppException", "configure_exception"]

logger = logging.getLogger(__name__)


class AppException(Exception):
    """应用异常基类"""

    def __init__(
        self,
        exc: HttpCode,
        *args,
        code: Optional[int] = None,
        msg: Optional[str] = None,
        echo_exc: bool = False,
        **kwargs,
    ):
        super().__init__()
        _code = code if code is not None else exc.code
        _message = msg if msg is not None else exc.msg
        self._code = _code or HttpResp.FAILED.code
        self._message = _message or HttpResp.FAILED.msg
        self.echo_exc = echo_exc
        self.kwargs = kwargs or {}
        self.args = args

    @property
    def code(self) -> int:
        return self._code

    @property
    def msg(self) -> str:
        return self._message

    def __str__(self):
        return f"{self.code}: {self.msg}"


def configure_exception(app: FastAPI):
    """配置全局异常处理"""

    @app.exception_handler(RequestValidationError)
    async def request_validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理请求参数验证的异常
        code: 310 311
        """
        resp = HttpResp.PARAMS_VALID_ERROR
        errs = exc.errors()
        if errs and errs[0].get("type", "").startswith("type_error."):
            resp = HttpResp.PARAMS_TYPE_ERROR
        logger.warning("validation_exception_handler: url=[%s], errs=[%s]", request.url.path, errs)
        res_content = {
            "code": resp.code,
            "msg": resp.msg,
            "data": errs[0],
        }
        request.state.track_info = res_content
        return JSONResponse(status_code=200, content=res_content)

    @app.exception_handler(ValidationError)
    async def validation_exception_handler(request: Request, exc: ValidationError) -> JSONResponse:
        """处理参数验证的异常 (除请求参数验证之外的)
        code: 500
        """
        resp = HttpResp.PARAMS_VALID_ERROR
        errs = exc.errors()
        if errs and errs[0].get("type", "").startswith("type_error."):
            resp = HttpResp.PARAMS_TYPE_ERROR
        logger.warning("validation_exception_handler: url=[%s], errs=[%s]", request.url.path, errs)
        res_content = {
            "code": resp.code,
            "msg": resp.msg,
            "data": errs[0],
        }
        request.state.track_info = res_content
        return JSONResponse(status_code=200, content=res_content)

    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
        """处理客户端请求异常
        code: 312 404
        """
        msg_ = f"http_exception_handler: url=[{request.url.path}], status_code=[{exc.status_code}]"
        logger.warning(msg_)
        resp = HttpResp.SYSTEM_ERROR
        if exc.status_code == 404:
            resp = HttpResp.REQUEST_404_ERROR
        elif exc.status_code == 405:
            resp = HttpResp.REQUEST_METHOD_ERROR
        elif exc.status_code == 335:
            resp = HttpResp.PROGRAM_NODE_EXISTS
        elif exc.status_code == 334:
            resp = HttpResp.DEVICE_ID_EXISTS
        res_content = {
            "code": resp.code,
            "msg": resp.msg,
            "data": {},
        }
        request.state.track_info = res_content
        return JSONResponse(status_code=200, content=res_content)

    @app.exception_handler(AssertionError)
    async def assert_exception_handler(request: Request, exc: AssertionError) -> JSONResponse:
        """处理断言异常
        code: 313
        """
        errs = ",".join(exc.args) if exc.args else HttpResp.ASSERT_ARGUMENT_ERROR.msg
        logger.warning("app_exception_handler: url=[%s], errs=[%s]", request.url.path, errs)
        res_content = {
            "code": HttpResp.ASSERT_ARGUMENT_ERROR.code,
            "msg": errs,
            "data": {},
        }
        request.state.track_info = res_content
        return JSONResponse(status_code=200, content=res_content)

    @app.exception_handler(AppException)
    async def app_exception_handler(request: Request, exc: AppException) -> JSONResponse:
        """处理自定义异常
        code: .
        """
        if exc.echo_exc:
            logger.error("app_exception_handler: url=[%s]", request.url.path)
            logger.error(exc, exc_info=True)
        res_content = {
            "code": exc.code,
            "msg": exc.msg,
            "data": {},
        }
        request.state.track_info = res_content
        return JSONResponse(status_code=200, content=res_content)

    @app.exception_handler(RedisConnectionError)
    async def db_redis_error_handler(request: Request, exc: RedisConnectionError) -> JSONResponse:
        """处理连接异常
        code: 500
        """
        logger.error("db_opr_error_handler: url=[%s]", request.url.path)
        logger.error(exc, exc_info=True)
        res_content = {
            "code": HttpResp.SYSTEM_ERROR.code,
            "msg": HttpResp.REDIS_ERROR.msg,
            "data": {},
        }
        request.state.track_info = res_content
        return JSONResponse(status_code=200, content=res_content)

    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """处理服务端异常, 全局异常处理
        code: 500
        """
        logger.error("global_exception_handler: url=[%s]", request.url.path)
        logger.error(exc, exc_info=True)
        res_data = {
            "code": HttpResp.SYSTEM_ERROR.code,
            "msg": HttpResp.SYSTEM_ERROR.msg,
            "data": {},
        }
        request.state.track_info = res_data
        return JSONResponse(status_code=200, content=res_data)
