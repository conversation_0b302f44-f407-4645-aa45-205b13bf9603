import json
from typing import Union, AsyncIterator, Set
from abc import ABC, abstractmethod

from fastapi import WebSocket
from pydantic import ValidationError
from starlette.websockets import WebSocketState

import apps.models.websocket as WSModel
from apps.utils.wsbroadcast import WBroadCast


class WebSockClient(ABC):
    """WebSocket 客户端基础类"""

    def __init__(self, wsock: WebSocket):
        self.wsock = wsock
        try:
            self.client_type = self.wsock.state.client_type
            self.client_id = self.wsock.state.client_id
        except AttributeError:
            print("ws client type is not found")
            self.client_id = ""
            self.client_type = "unknown"
        self.rooms: Set[str] = set()

    @abstractmethod
    async def init(self):
        """初始化资源"""
        pass

    @abstractmethod
    async def clean(self):
        """清理资源"""
        pass

    @abstractmethod
    async def process_msg(self):
        """处理客户端 socket 消息"""
        pass  # pylint: disable=unnecessary-pass

    @abstractmethod
    async def process_room_msg(self, data: WSModel.MsgModel):
        """处理房间接受的消息"""
        pass  # pylint: disable=unnecessary-pass

    def parse_data(self, data: str) -> Union[None, WSModel.MsgModel]:
        """解析数据"""
        try:
            json_data = json.loads(data)
            return WSModel.MsgModel(**json_data)
        except json.JSONDecodeError:
            print(f"websocket msg json decode error, {data}")
        except ValidationError as e:
            print(f"websocket room msg format error, {json_data}, fail: {e}")
        return None

    async def recv_socket_msg(self) -> AsyncIterator[WSModel.MsgModel]:
        """接受客户端socket消息"""
        async for data in self.wsock.iter_text():
            msg = self.parse_data(data)
            if msg is None:
                continue
            yield msg

    async def send_msg(self, msg: str):
        """发送消息"""
        if self.wsock.client_state != WebSocketState.CONNECTED:
            print("client state is not connected")
            return
        await self.wsock.send_text(msg)

    async def broadcast_room_msg(self, room_id, msg: WSModel.MsgModel):
        """发送消息到房间"""
        await WBroadCast.publish(room_id, msg.model_dump_json())

    async def send_ping_msg(self):
        """处理房间消息"""
        msg_ = WSModel.MsgModel(cmd="ping", data={})
        await self.send_msg(msg_.model_dump_json())

    async def send_pong_msg(self, msg: WSModel.MsgModel):
        """发送pingpong消息到socket房间"""
        msg_ = WSModel.MsgModel(id=msg.id, cmd="pong", data={})
        await self.send_msg(msg_.model_dump_json())

    async def send_join_room_msg(self, room_id):
        """发送加入房间消息"""
        msg_data = {"id": self.client_id, "type": self.client_type}
        msg_ = WSModel.MsgModel(cmd="room.join", user=self.client_id, data=msg_data, room_id=room_id)
        await self.broadcast_room_msg(room_id, msg_)

    async def send_leave_room_msg(self, room_id):
        """发送离开房间消息"""
        msg_data = {"client_id": self.client_id, "client_type": self.client_type}
        msg_ = WSModel.MsgModel(cmd="room.leave", user=self.client_id, data=msg_data, room_id=room_id)
        await self.broadcast_room_msg(room_id, msg_)

    async def sub_room_msg(self, room_id: str):
        # 取消订阅, 如果已经订阅
        if room_id in self.rooms:
            print(f"room {room_id} is already sub")
            return

        self.rooms.add(room_id)
        try:
            await self.send_join_room_msg(room_id)
            async with WBroadCast.subscribe(room_id) as subscriber:
                while True:
                    event = await subscriber.get()
                    if room_id not in self.rooms:
                        print(f"room {room_id} is closed")
                        break
                    msg = self.parse_data(event.message)
                    if msg is None:
                        continue
                    await self.process_room_msg(msg)
        except Exception as e:
            print(f"sub room msg error: {e}")
        finally:
            if room_id in self.rooms:
                self.rooms.remove(room_id)
                await self.send_leave_room_msg(room_id)

    async def unsub_room_msg(self, room_id):
        """取消订阅房间消息"""
        try:
            self.rooms.remove(room_id)
            await self.send_leave_room_msg(room_id)
        except KeyError:
            pass

    async def room_switch(self, msg: WSModel.MsgModel, tg):
        """加入或离开房间"""
        res_msg = WSModel.MsgModel(cmd="alarm.info", data={"msg": ""})
        try:
            vehicle_id = msg.data["vehicle_id"]
            room_id = f"room.{vehicle_id}"
            if msg.cmd == "room.join":
                tg.start_soon(self.sub_room_msg, room_id)
                res_msg.data["msg"] = "join room success"
            elif msg.cmd == "room.leave":
                await self.unsub_room_msg(room_id)
                res_msg.data["msg"] = "leave room success"
            else:
                res_msg.cmd = "alarm.error"
                res_msg.data["msg"] = "cmd error"
        except KeyError:
            res_msg.cmd = "alarm.error"
            res_msg.data["msg"] = "vehicle_id is required"
        await self.send_msg(res_msg.model_dump_json())
