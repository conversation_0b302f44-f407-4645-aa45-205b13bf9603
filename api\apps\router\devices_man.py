from typing import Dict

from fastapi import APIRouter, Request

from apps.common import unified_resp
import apps.services.opconsole as OPCService
import apps.services.vehicles as VehicleService
from apps.services.permissions import gen_dp, FuncId
from apps.services.common import get_header_info


router = APIRouter(prefix="/devices", tags=["设备管理"])


@router.get("/vehicles/un_register_list", dependencies=gen_dp(FuncId.DeviceManage))
@unified_resp
async def vehicle_un_register_list(_: Request):
    """获取未注册设备列表"""
    return await VehicleService.unregister_device_list()


@router.get("/op_consoles/un_register_list", dependencies=gen_dp(FuncId.DeviceManage))
@unified_resp
async def opc_un_register_list(_: Request):
    """获取未注册设备列表"""
    return await OPCService.unregister_device_list()


@router.get("/op_consoles/registration/{device_id}")
@unified_resp
async def get_opc_for_device(req: Request, device_id: str):
    """获取未注册设备列表"""
    device_info: Dict = get_header_info(req)
    return await OPCService.get_opc_id_for_device(device_id, device_info)
