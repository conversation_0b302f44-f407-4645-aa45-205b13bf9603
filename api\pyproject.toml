[tool.black]
line-length = 120
target-version = ['py310']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"


[tool.poetry]
name = "api"
version = "1.1.1"
description = ""
authors = ["chen"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
fastapi = {extras = ["standard"], version = "^0.115.6"}
uvicorn = "^0.32.0"
pydantic = {extras = ["email"], version = "^2.9.2"}
pydantic-settings = "^2.6.0"
broadcaster = "^0.3.1"
PyYAML = "^6.0.2"
websockets = "^13.1"
motor = "^3.6.0"
influxdb-client = "^1.47.0"
redis = "^5.2.0"
python-multipart = "^0.0.16"
httpx = "^0.27.2"
aiocsv = "^1.3.2"
aiohttp = "^3.11.2"
arrow = "^1.3.0"
yagmail = "^0.15.293"
pillow = "^11.3.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
pytest-dependency = "^0.6.0"
mypy = "^1.13.0"

[[tool.poetry.source]]
name = "tuna"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
# strict = true
strict_equality = true
files = ["apps", "main.py"]
exclude = ["tests", "temp", "__init__.py"]
disable_error_code = ["attr-defined"]

# 生成文档
# poetry export -f requirements.txt --output requirements.txt