<template>
  <div class="">
    <div class="head h-[48px] bg-white py-2 px-2 rounded-[4px]">
      <el-page-header @back="$router.back()">
        <template #content>
          <div class="flex items-center">
            <span class="text-large font-600 mr-3"> {{ projectDetail.prj_name }} </span>
          </div>
        </template>
        <template #extra>
          <div class="flex items-center">
            <el-button v-if="!pageData.idEdit" type="info" @click="changeType">编辑</el-button>
            <el-button v-else type="primary" @click="changeType">保存</el-button>
          </div>
        </template>
      </el-page-header>
    </div>
    <div class="mt-2 h-[calc(100vh-48px)] rounded-[4px]">
      <div class="flex flex-row base-map-timeline-wrap">
        <div class="base-map-wrap flex">
          <div class="base-wrap project-item bg-white rounded-[4px] h-[254px]">
            <div class="title">
              <div>基本信息</div>
              <div>
                <el-button v-if="pageData.idEdit" type="primary" size="small" @click="showBaseEditDialog">
                  编辑
                </el-button>
              </div>
            </div>
            <BaseShow
              :prj_name="projectDetail.prj_name"
              :prj_status="projectDetail.prj_status"
              :prj_address="projectDetail.prj_address"
              :devices_info="projectDetail.devices_info"
              :first_party="projectDetail.first_party"
              :salesperson="projectDetailSalesperson"
              :installer_info="projectDetail.installer_info"
            ></BaseShow>
          </div>
          <div class="map-wrap project-item bg-white rounded-[4px] h-[254px]">
            <div class="title">
              <div>位置信息</div>
              <div>
                <el-button v-if="pageData.idEdit" type="primary" size="small" @click="showMapEditDialog">
                  编辑
                </el-button>
              </div>
            </div>
            <div class="content pt-2 h-[88%]">
              <MapShow
                ref="mapShowRef"
                :lng="projectDetail.prj_position[0]"
                :lat="projectDetail.prj_position[1]"
              ></MapShow>
            </div>
          </div>
        </div>
        <div class="timeline-wrap project-item bg-white rounded-[4px]">
          <div class="title">
            <div>当前情况</div>
            <div>
              <el-button v-if="pageData.idEdit" type="primary" size="small" @click="showProgressEditDialog"
                >更新状态</el-button
              >
            </div>
          </div>
          <div class="content ml-2 p-3">
            <el-timeline style="">
              <el-timeline-item
                v-for="(activity, index) in projectDetail.prj_progress"
                :key="index"
                :type="activity.progress_type"
                :color="activity.color"
                :size="activity.size"
                :hollow="activity.hollow"
                :timestamp="activity.timestamp"
              >
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
      <div class="flex flex-col device-wrap mt-[10px]">
        <div class="vehicle-wrap bg-white rounded-[4px] p-2">
          <div class="title">
            <div>车辆信息</div>
            <div>
              <el-button v-if="pageData.idEdit" type="primary" size="small" @click="showVehicleEditDialog"
                >新增车辆</el-button
              >
            </div>
          </div>
          <div class="content pt-2">
            <VehiccleItem
              v-for="item in projectDetail.vehicle"
              :vehicle_id="item.vehicle_id"
              :vehicle_name="item.vehicle_name"
              :vehicle_type="item.vehicle_type"
              :vehicle_sn="item.vehicle_sn"
              :installer="item.installer"
              :smart_feature="item.smart_feature"
              @edit="handleVehicleEdit"
              @copy="handleVehicleCopy"
              @delete="handleVehicleDelete"
            ></VehiccleItem>
          </div>
        </div>
        <div class="console-wrap bg-white rounded-[4px] h-[354px] p-2 mt-[10px]">
          <div class="title">
            <div>操作台信息</div>
            <div>
              <el-button v-if="pageData.idEdit" type="primary" size="small" @click="showConsoleEditDialog">
                新增操作台
              </el-button>
            </div>
          </div>
          <div class="content pt-2">
            <ConsoleItem
              v-for="item in projectDetail.console"
              :console_id="item.console_id"
              :console_name="item.console_name"
              :console_type="item.console_type"
              :console_sn="item.console_sn"
              @edit="handleConsoleEdit"
              @copy="handleConsoleCopy"
              @delete="handleConsoleDelete"
            ></ConsoleItem>
          </div>
        </div>
      </div>
      <div class="console-wrap bg-white rounded-[4px] h-[354px] p-2 mt-[10px]">
        <div class="title">
          <div>工单信息</div>
          <div>
            <el-button v-if="pageData.idEdit" type="primary" size="small">编辑</el-button>
          </div>
        </div>
        <div class="content pt-2">
          <el-table :data="projectDetail.work_order">
            <el-table-column label="ID" prop="id" />
            <el-table-column label="现象" prop="symptom" />
            <el-table-column label="状态" prop="status" />
            <el-table-column label="描述" prop="description" />
            <el-table-column label="反馈人" prop="feedback_user" />
            <el-table-column label="存留时长" prop="duration" />
          </el-table>
        </div>
      </div>
      <div class="console-wrap bg-white rounded-[4px] h-[354px] p-2 mt-[10px]">
        <div class="title">
          <div>甘特图</div>
          <div>
            <el-button v-if="pageData.idEdit" type="primary" size="small">编辑</el-button>
          </div>
        </div>
        <div class="content pt-1"></div>
      </div>
    </div>
    <MapEditDialog ref="mapEditDialogRef" v-if="pageData.isShowMapEditDialog" @success="handleMapEdit"></MapEditDialog>
    <BaseEditDialog
      ref="baseEditRef"
      v-if="pageData.idShowBaseEditDialog"
      @success="handleBaseEdit"
      @close="pageData.idShowBaseEditDialog = false"
    ></BaseEditDialog>
    <ProgressEditDialog
      ref="progressEditDialogRef"
      v-if="pageData.isShowProgressEditDialog"
      @success="handleProgressEdit"
    ></ProgressEditDialog>
    <VehicleEditDialog
      :id="projectDetail.id"
      ref="vehicleEditDialogRef"
      v-if="pageData.isShowVehicleEditDialog"
      @success="getProjectDetail()"
      @close="pageData.isShowVehicleEditDialog = false"
    ></VehicleEditDialog>
    <ConsoleEditDialog
      :id="projectDetail.id"
      ref="consoleEditDialogRef"
      v-if="pageData.isShowConsoleEditDialog"
      @success="getProjectDetail()"
    ></ConsoleEditDialog>
  </div>
</template>

<script setup lang="ts">
import feedback from "@/utils/feedback";

import BaseShow from "./components/base-show.vue";
import MapShow from "./components/map-show.vue";
import MapEditDialog from "./components/map-edit.vue";
import VehiccleItem from "./components/vehicle-item.vue";
import ConsoleItem from "./components/console-item.vue";
import BaseEditDialog from "./components/base-edit.vue";
import ProgressEditDialog from "./components/progress-edit.vue";
import VehicleEditDialog from "./components/vehicle-edit.vue";
import ConsoleEditDialog from "./components/console-edit.vue";

import {
  projectDetail as projectDetailApi,
  projectBaseEdit,
  projectLocationEdit,
  projectProgressEdit,
  projectDeleteVehicle,
  projectDeleteConsole,
} from "@/api/project-man";

const route = useRoute();
const mapShowRef = shallowRef<InstanceType<typeof MapShow>>();
const mapEditDialogRef = shallowRef<InstanceType<typeof MapEditDialog>>();
const baseEditRef = shallowRef<InstanceType<typeof BaseEditDialog>>();
const progressEditDialogRef = shallowRef<InstanceType<typeof ProgressEditDialog>>();
const vehicleEditDialogRef = shallowRef<InstanceType<typeof VehicleEditDialog>>();
const consoleEditDialogRef = shallowRef<InstanceType<typeof ConsoleEditDialog>>();

const pageData = reactive({
  idEdit: false,
  idShowBaseEditDialog: false,
  isShowMapEditDialog: false,
  isShowProgressEditDialog: false,
  isShowVehicleEditDialog: false,
  isShowConsoleEditDialog: false,
});

const projectDetail = reactive({
  id: "",
  prj_name: "项目名称",
  prj_status: 0, // 0：未开始 1：已完成 2：进行中 3：有工单
  prj_desc: "", // 项目描述
  first_party: "", // 甲方单位
  salesperson: [], // 销售经理
  is_public_network: false, // 是否公网，默认否
  is_oms: false, // 是否接入管理平台，默认否
  devices_info: "", // 设备信息
  installer_info: "", // 现场工程师
  prj_position: [0, 0], // 项目位置
  prj_address: "北京市朝阳区呼家楼街道燕子胡同朝外SOHO-A座", // 项目地址
  prj_progress: [
    {
      content: "项目创建",
      timestamp: "2025-01-01 00:01",
      progress_type: "primary",
      hollow: true,
      color: "#0bbd87",
      size: "large",
    },
  ], // 项目时间线，项目的生命周期，从开始到完成
  vehicle: [
    // {
    //   name: "卡特305",
    //   type: 1,
    //   sn: "", // 工控机唯一id
    //   intelligent_function: [], // 智能化功能
    //   installer_name: "",
    //   installer_id: "",
    //   status: 1, // 0：未开始 1：已完成 2：进行中
    //   guide_id: "1234",
    //   node_completed: [], // 已经完成的节点，如果指导更改，动态计算出完成量
    //   guide_timeline: [
    //     {
    //       id: 2,
    //       time: "2024-12-12 12:12:12",
    //       content: "主视摄像头已安装",
    //       user: "",
    //       user_id: "",
    //     },
    //   ],
    //   completion_rate: 20, // 动态计算完成率，不需要返回 完成节点数量 / 作业指导总节点
    //   vehicle_id: "", // 如果接入平台，手动设置id，并可以跳转
    //   ros_version: "", // 工控机版本
    // },
    {
      vehicle_id: "",
      vehicle_name: "",
      vehicle_type: 1,
      vehicle_sn: "",
      installer: [],
      guide_id: "",
      oms_id: "",
      smart_feature: [],
    },
  ],
  console: [
    {
      console_id: "",
      console_name: "",
      console_type: 1,
      console_sn: "",
    },
  ],
  work_order: [
    // 该项目的所有工单
    {
      id: 1,
      symptom: "摄像头无法显示",
      status: "未解决",
      description: "右视无法显示",
      feedback_user: "田少雄",
      duration: "1天3小时",
    },
  ],
});

const projectDetailSalesperson = computed(() => {
  return projectDetail.salesperson.map((item: any) => item.nickname).join(",");
});

const changeType = () => {
  pageData.idEdit = !pageData.idEdit;
};

const showBaseEditDialog = async () => {
  pageData.idShowBaseEditDialog = true;
  await nextTick();
  baseEditRef.value?.setFormData(projectDetail);
  baseEditRef.value?.open();
};

const showMapEditDialog = async () => {
  pageData.isShowMapEditDialog = true;
  await nextTick();
  mapEditDialogRef.value?.open();
};

const showProgressEditDialog = async () => {
  pageData.isShowProgressEditDialog = true;
  await nextTick();
  progressEditDialogRef.value?.open();
};

const showVehicleEditDialog = async () => {
  pageData.isShowVehicleEditDialog = true;
  await nextTick();
  vehicleEditDialogRef.value?.open();
};

const showConsoleEditDialog = async () => {
  pageData.isShowConsoleEditDialog = true;
  await nextTick();
  consoleEditDialogRef.value?.open();
};

const handleBaseEdit = async (data: any) => {
  Object.assign(projectDetail, data);
  await projectBaseEdit(data);
  pageData.idShowBaseEditDialog = false;
};

const handleMapEdit = async (data: any) => {
  Object.assign(projectDetail, data);
  await projectLocationEdit({
    id: projectDetail.id,
    ...data,
  });
  pageData.isShowMapEditDialog = false;
  mapShowRef.value?.reload();
};

const handleProgressEdit = async (data: any) => {
  await projectProgressEdit({
    id: projectDetail.id,
    ...data,
  });
  pageData.isShowProgressEditDialog = false;
  getProjectDetail(projectDetail.id);
};

const handleVehicleEdit = async (id: any) => {
  pageData.isShowVehicleEditDialog = true;
  await nextTick();
  vehicleEditDialogRef.value?.open("edit");
  const targetVehicle = projectDetail.vehicle.find((item: any) => item.vehicle_id == id);
  vehicleEditDialogRef.value?.setFormData(targetVehicle);
};

const handleVehicleCopy = async () => {
  pageData.isShowVehicleEditDialog = true;
  await nextTick();
  vehicleEditDialogRef.value?.open("add");
};

const handleVehicleDelete = async (vehicle_id: string) => {
  await feedback.confirm("确定要删除？");
  await projectDeleteVehicle({
    id: projectDetail.id,
    vehicle_id: vehicle_id,
  });
  feedback.msgSuccess("删除成功");
  getProjectDetail(projectDetail.id);
};

const handleConsoleEdit = async (id: any) => {
  pageData.isShowConsoleEditDialog = true;
  await nextTick();
  consoleEditDialogRef.value?.open("edit");
  const targetConsole = projectDetail.console.find((item: any) => item.console_id == id);
  consoleEditDialogRef.value?.setFormData(targetConsole);
};

const handleConsoleCopy = async () => {
  pageData.isShowConsoleEditDialog = true;
  await nextTick();
  consoleEditDialogRef.value?.open("add");
};

const handleConsoleDelete = async (console_id: string) => {
  await feedback.confirm("确定要删除？");
  await projectDeleteConsole({
    id: projectDetail.id,
    console_id: console_id,
  });
  feedback.msgSuccess("删除成功");
  getProjectDetail(projectDetail.id);
};

const handleSave = () => {
  console.log("handleSave");
};

const getProjectDetail = async (id: string = projectDetail.id) => {
  const data = await projectDetailApi(id);
  Object.assign(projectDetail, data);
  mapShowRef.value?.reload();
};

onMounted(() => {
  console.log(route.query);
  const { id, type } = route.query;

  if (type === "edit") {
    pageData.idEdit = true;
  }

  if (id) {
    getProjectDetail(id as string);
  }
});
</script>

<style scoped lang="scss">
.project-item {
  padding: 8px;
}

.title {
  border-left: 2px solid var(--el-color-primary);
  padding-left: 6px;
  font-size: 16px;
  font-weight: 500;
  height: 26px;
  line-height: 26px;
  display: flex;
  justify-content: space-between;
}

.vehicle-wrap {
  height: 374px;
  .content {
    display: flex;
    flex-direction: row;
    height: 330px;
    overflow-x: auto;
    overflow-y: hidden;
  }
}

.console-wrap {
  height: 334px;
  .content {
    display: flex;
    flex-direction: row;
    height: 280px;
    overflow-x: auto;
    overflow-y: hidden;
  }
}

@media screen and (min-width: 1480px) {
  .base-map-wrap + .timeline-wrap {
    margin-left: 10px;
  }
  .base-map-wrap {
    flex: 2;
  }
  .timeline-wrap {
    flex: 1;
    height: 254px;
    .content {
      height: 88%;
      overflow-y: auto;
    }
  }
  .base-map-wrap {
    flex-direction: row;
  }
  .project-item {
    flex: 1;
    margin-left: 10px;
  }
  .project-item:first-child {
    margin-left: 0;
  }
}

@media (min-width: 740px) and (max-width: 1479px) {
  .base-map-wrap + .timeline-wrap {
    margin-left: 10px;
  }
  .base-wrap + .map-wrap {
    margin-top: 10px;
  }

  .base-map-wrap {
    flex: 1;
  }
  .timeline-wrap {
    flex: 1;
    height: 518px;
    .content {
      margin-top: 8px;
      height: 92%;
      overflow-y: auto;
    }
  }
  .base-map-wrap {
    flex-direction: column;
  }
}

@media (max-width: 739px) {
  .base-map-wrap + .timeline-wrap {
    margin-top: 10px;
  }
  .base-wrap + .map-wrap {
    margin-top: 10px;
  }
  .base-map-wrap {
    flex-direction: column;
  }
  .base-map-timeline-wrap {
    flex-direction: column;
  }
  .timeline-wrap {
    height: 354px;
    .content {
      height: 88%;
      overflow-y: auto;
    }
  }
}
</style>
