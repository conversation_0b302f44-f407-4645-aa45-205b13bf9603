<template>
  <popup
    custom-class="no-footer"
    ref="popupRef"
    title="基本情報"
    width="550px"
    @confirm="handleSubmit"
    @close="handleClose"
    :confirmButtonText="$t('stream.确定')"
    :cancelButtonText="$t('stream.取消')"
  >
    <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
      <el-form-item label="お客様名" prop="customer_name">
        <el-input v-model="formData.customer_name" placeholder="入力してください" clearable />
      </el-form-item>
      <el-form-item label="担当者名" prop="person_name">
        <el-input v-model="formData.person_name" placeholder="入力してください" clearable />
      </el-form-item>
      <el-form-item label="連絡先" prop="contact_info">
        <el-input v-model="formData.contact_info" placeholder="入力してください" clearable />
      </el-form-item>
    </el-form>
  </popup>
</template>

<script lang="ts" setup>
import Popup from "@/components/popup/index.vue";
import { getUserList } from "../util";

const emit = defineEmits(["success", "close"]);
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const formData: any = reactive({
  id: "",
  customer_name: "",
  contact_info: "",
  person_name: "",
});

const formRules = reactive({
  customer_name: [
    {
      required: true,
      message: "入力してください",
      trigger: "blur",
    },
  ],
});

const open = () => {
  popupRef.value?.open();
};

const handleClose = () => {
  emit("close");
};

const handleSubmit = () => {
  emit("success", formData);
  handleClose();
};

const setFormData = async (data: any) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

onMounted(async () => {});

onUnmounted(() => {});

defineExpose({
  open,
  setFormData,
});
</script>

<style scoped></style>
