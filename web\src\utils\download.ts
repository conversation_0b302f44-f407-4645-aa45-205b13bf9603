/**
 * 文件下载工具函数
 * 支持强制下载各种文件类型，避免浏览器预览
 */
import feedback from "@/utils/feedback";


/**
 * 强制下载文件的后备方法
 * 专门处理同源策略限制的情况
 */
async function forceDownloadFile(url: string, fileName: string): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // 创建一个隐藏的 a 标签
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.download = fileName;

      // 添加强制下载的属性
      link.setAttribute("download", fileName);

      // 对于图片等可预览文件，尝试修改 URL 添加下载参数
      const downloadUrl = url.includes("?") ? `${url}&download=1` : `${url}?download=1`;
      link.href = downloadUrl;

      document.body.appendChild(link);

      // 触发点击
      link.click();

      // 清理
      setTimeout(() => {
        if (document.body.contains(link)) {
          document.body.removeChild(link);
        }
      }, 100);

      resolve();
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 改进的下载函数，支持多种下载策略
 */
export async function downloadFile(url: string, fileName?: string): Promise<void> {
  const finalFileName = fileName || "download";

  // 如果 Blob 方法失败，使用强制下载方法
  try {
    await forceDownloadFile(url, finalFileName);
  } catch (fallbackError) {
    console.error("所有下载方法都失败:", fallbackError);

    // 最后的后备方案：直接打开链接并提示用户
    window.open(url, "_blank");
    feedback.msgWarning('无法自动下载文件，已在新窗口打开。请右键点击文件并选择"另存为"进行下载。');
  }
}
