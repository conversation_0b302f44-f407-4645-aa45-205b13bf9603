from typing import Annotated

from fastapi import APIRouter, Request, Query, Depends
from fastapi.security import OAuth2PasswordRequestForm

from apps.common import unified_resp
from apps.models.common import ObjectIdStr
import apps.models.user as UserModel
from apps.services.user import UserService
from apps.services.permissions import gen_dp, FuncId

router = APIRouter(prefix="/user", tags=["用户管理"])


@router.get("/", dependencies=gen_dp(FuncId.UserManage))
@unified_resp
async def user_list(_: Request, q: Annotated[UserModel.Query, Query()]):
    res_data = await UserService().list(q)
    return res_data


@router.post("/", dependencies=gen_dp(FuncId.UserManage))
@unified_resp
async def user_add(_: Request, user: UserModel.Create):
    user_srv = UserService()
    return await user_srv.create(user)


@router.put("/", dependencies=gen_dp(FuncId.UserManage))
@unified_resp
async def user_info_update(_: Request, user: UserModel.Update):
    user_srv = UserService()
    return await user_srv.update(user)


@router.delete("/", dependencies=gen_dp(FuncId.UserManage))
@unified_resp
async def user_delete(_: Request, uid: ObjectIdStr):
    user_srv = UserService()
    return await user_srv.delete(uid=uid)


@router.get("/detail")
@unified_resp
async def user_detail(_: Request, uid: ObjectIdStr):
    # 用于管理员查看用户详情
    user_srv = UserService()
    return await user_srv.detail(uid=uid)


@router.put("/status", dependencies=gen_dp(FuncId.UserManage))
@unified_resp
async def user_status_update(_: Request, user: UserModel.ChangeStatus):
    user_srv = UserService()
    return await user_srv.update_status(uid=user.id, status=user.status)


#################### 登录登出 ####################
@router.post("/login")
@unified_resp
async def login(req: Request, data: UserModel.Login):
    user_srv = UserService()
    return await user_srv.login(data, req)


@router.post("/auth")
async def auth(form_data: OAuth2PasswordRequestForm = Depends()):
    """兼容OAuth2认证, 同login接口功能一致"""
    data = UserModel.Login(
        grant_type=form_data.grant_type,
        username=form_data.username,
        password=form_data.password,
    )
    user_srv = UserService()
    res_data = await user_srv.login(data)
    return {"access_token": res_data["token"], "token_type": "bearer"}


@router.get("/logout")
@unified_resp
async def logout(req: Request):
    user_srv = UserService()
    return await user_srv.logout(req)


#################### 自身部分接口 ####################
@router.get("/info")
@unified_resp
async def info(req: Request):
    # 用于用户自己查看自己详情
    user_srv = UserService()
    return await user_srv.info(req)


@router.get("/me")
@unified_resp
async def user_detail_self(req: Request):
    user_srv = UserService()
    return await user_srv.info(req)


@router.put("/me")
@unified_resp
async def user_update(_: Request, user: UserModel.Update):
    user_srv = UserService()
    return await user_srv.update(user=user)


@router.put("/me/password")
@unified_resp
async def user_update_password(_: Request, user: UserModel.ChangePass):
    user_srv = UserService()
    return await user_srv.update_password(user.id, user.new_password, user.old_password)
