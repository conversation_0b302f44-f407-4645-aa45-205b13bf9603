from typing import Annotated

from fastapi import APIRouter, WebSocket, Path

from apps.services.permissions import Access
import apps.services.websockets as WService


router = APIRouter(prefix="/ws", tags=["Websockets管理"])


NVIDIA_DEVICE_ID = Path(..., title="设备ID", description="米文设备", min_length=13, max_length=13)
ANDROID_DEVICE_ID = Path(..., title="设备ID", description="安卓设备", min_length=16, max_length=16)
USER_TOKEN_ID = Path(..., title="用户TOKEN", description="用户", min_length=25, max_length=25)


@router.websocket("/vehicle/{device_id}")
async def vehicle_device_endpoint(wsock: WebSocket, device_id: Annotated[str, NVIDIA_DEVICE_ID]):
    """处理车端设备 WebScoket 连接"""
    await WService.process_vehicle(device_id, wsock)


@router.websocket("/op_console/{device_id}")
async def opc_device_endpoint(wsock: WebSocket, device_id: Annotated[str, ANDROID_DEVICE_ID]):
    """处理操作台 WebScoket 连接"""
    await Access().ws(wsock)
    await WService.process_opc(device_id, wsock)


@router.websocket("/user")
async def browser_user_endpoint(wsock: WebSocket):
    """处理浏览器用户 WebScoket 连接"""
    await Access().ws(wsock)
    await WService.process_user(wsock)
