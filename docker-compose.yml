
services:
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: project-admin-web
    restart: always
    ports:
      - "80:80"
    depends_on:
      - api
    networks:
      - project-network

  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: project-admin-api
    restart: always
    ports:
      - "8800:8800"
    volumes:
      - ./api:/app
    environment:
      MODE: prod
      MONGO_HOST: mongo
      REDIS_HOST: redis
    depends_on:
      - mongo
      - redis
    networks:
      - project-network

  mongo:
    image: mongo:7.0
    container_name: project-admin-mongo
    restart: always
    environment:
      TZ: Asia/Shanghai
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: Chen1234
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"
    networks:
      - project-network

  redis:
    image: redis:7.4
    container_name: project-admin-redis
    restart: always
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - project-network

volumes:
  mongo_data:
  redis_data:

networks:
  project-network: