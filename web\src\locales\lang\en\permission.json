{"人员账号": "User Account", "人员名称": "User Name", "人员角色": "User Role", "查询": "Search", "重置": "Reset", "账号": "Account", "名称": "Name", "角色": "Role", "邮箱": "Email", "关联车辆": "Associated Vehicle", "状态": "Status", "密码": "Password", "确认密码": "Confirm Password", "请输入账号": "Please Enter Account", "请输入名称": "Please Enter Name", "请选择车辆": "Please Select Vehicle", "创建时间": "Creation Time", "最近登录时间": "Last Login Time", "编辑人员": "Edit User", "新增角色": "Add New Role", "备注": "Remarks", "排序": "Sort", "正常": "Active", "停用": "Inactive", "权限": "Permissions", "权限设置": "Permission Settings", "展开折叠": "Expand/Collapse", "全选不全选": "Select All/Deselect All", "父子联动": "Parent-<PERSON>", "新增菜单": "Add New Menu", "下载当前菜单": "Download Current Menu", "上传菜单": "Upload Menu", "图标": "Icon", "权限标识": "Permission Identifier", "更新时间": "Update Time", "菜单类型": "Menu Type", "目录": "Directory", "菜单": "<PERSON><PERSON>", "按钮": "<PERSON><PERSON>", "父级菜单": "<PERSON><PERSON>", "请选择父级菜单": "Please Select <PERSON><PERSON>", "菜单名称": "<PERSON>u Name", "请输入菜单名称": "Please Enter Menu Name", "菜单图标": "Menu Icon", "无": "None", "搜索图标": "Search Icon", "路由路径": "Route Path", "请输入路由路径": "Please Enter Route Path", "访问的路由地址": "Access Route Address, e.g., `admin`, if external, start with `http(s)://`", "是否显示": "Show", "显示": "Visible", "隐藏": "Hidden", "选择隐藏则路由将不会出现在侧边栏": "If Hidden, the Route will not appear in the sidebar but can still be accessed", "菜单状态": "Menu Status", "选择停用则路由将不会出现在侧边栏": "If Inactive, the Route will not appear in the sidebar and cannot be accessed", "菜单排序": "Menu Order", "数值越大越排前": "Higher Values Appear First", "组件路径": "Component Path", "请输入组件路径": "Please Enter Component Path", "访问的组件路径": "Component Path, e.g., `permission/admin/index`, default is in the `views` directory", "选中菜单": "Selected Menu", "请输入选中菜单": "Please Enter Selected Menu", "访问详情页面菜单高亮显示": "Highlight Menu on Details or Edit Page, e.g., `/consumer/lists`", "权限字符": "Permission Character", "请输入权限字符": "Please Enter Permission Character", "将作为验权使用": "Used for Server-side API Authorization, e.g., `system:admin:list`, Modify with Caution", "路由参数": "Route Parameters", "请输入路由参数": "Please Enter Route Parameters", "访问路由的默认传递参数": "Default Route Parameters, e.g., `{id: 1, name: admin}` or `id=1&name=admin`", "是否缓存": "<PERSON><PERSON>", "缓存": "<PERSON><PERSON>", "不缓存": "Do Not Cache", "选择缓存则会被缓存": "If Cached, will be stored in `keep-alive`"}