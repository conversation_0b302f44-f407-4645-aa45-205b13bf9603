<template>
  <div class="upload">
    <el-upload
      class="w-full flex flex-col justify-center items-center"
      ref="uploadRefs"
      v-model:file-list="fileList"
      :drag="isDrag"
      :action="action"
      :multiple="multiple"
      :limit="limit"
      :show-file-list="showFileList"
      :list-type="listType"
      :headers="headers"
      :data="data"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-exceed="handleExceed"
      :on-error="handleError"
      :on-remove="handleRemove"
      :accept="getAccept"
    >
      <template #file="{ file }">
        <slot name="miniImg" :file="file"></slot>
      </template>
      <el-icon v-if="showFileList"><Plus /></el-icon>
      <slot></slot>
      <template #tip>
        <slot name="tip"></slot>
      </template>
    </el-upload>
    <el-dialog
      v-if="showProgress && fileList.length"
      v-model="visible"
      title="上传进度"
      :close-on-click-modal="false"
      width="500px"
      :modal="false"
      @close="handleClose"
    >
      <div class="file-list p-4">
        <template v-for="(item, index) in fileList" :key="index">
          <div class="mb-5">
            <div>{{ item.name }}</div>
            <div class="flex-1">
              <el-progress :percentage="parseInt(item.percentage)"></el-progress>
            </div>
          </div>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, ref, shallowRef, watch, toRaw } from "vue";
import useUserStore from "@/stores/modules/user";
import config from "@/config";
import feedback from "@/utils/feedback";
import type { ElUpload, UploadProps, UploadUserFile } from "element-plus";
import { RequestCodeEnum } from "@/enums/requestEnums";
export default defineComponent({
  components: {},
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 多选时最多选择几条
    limit: {
      type: Number,
      default: 10,
    },
    // 上传时的额外参数
    data: {
      type: Object,
      default: () => ({}),
    },
    // 是否显示上传进度
    showProgress: {
      type: Boolean,
      default: false,
    },
    // 拖拽上传
    isDrag: {
      type: Boolean,
      default: false,
    },
    showFileList: {
      type: Boolean,
      default: false,
    },
    listType: {
      type: String as () => "text" | "picture" | "picture-card",
      default: "text",
    },
  },
  emits: ["success", "error"],
  setup(props, { emit }) {
    const userStore = useUserStore();
    const uploadRefs = shallowRef<InstanceType<typeof ElUpload>>();
    const action = ref(`${config.baseUrl}${config.urlPrefix}/v1/material/upload`);
    const headers = computed(() => ({
      authorization: userStore.token,
      version: config.version,
    }));
    const visible = ref(false);
    const fileList = ref<any[]>([]);

    watch(
      () => props.modelValue,
      (value) => {
        fileList.value = toRaw(value);
      },
      {
        immediate: true,
      }
    );

    const handleProgress = (event: any, file: any, fileLists: any[]) => {
      // visible.value = true;
      fileList.value = toRaw(fileLists);
    };

    const handleSuccess = (response: any, file: any, fileLists: any[]) => {
      const allSuccess = fileLists.every((item) => item.status == "success");
      if (allSuccess) {
        if (!props.showFileList) uploadRefs.value?.clearFiles();
        visible.value = false;
        // console.log("成功 fileLists", fileLists);

        const getMaterialUrl = (id: any) => {
          let fileUrl = import.meta.env.VITE_FILE_SERVER;
          if (import.meta.env.PROD) fileUrl = window.document.location.origin;
          return `${fileUrl}/api/v1/material/content/${id}`;
        };

        let nameUrlList = fileLists.map((item) => {
          if (item.response) {
            return {
              name: item.name,
              url: getMaterialUrl(item.response.data.id),
            };
          } else {
            return {
              name: item.name || "",
              url: item.url || "",
            };
          }
        });

        emit("success", file, nameUrlList);
      }
      if (response.code == RequestCodeEnum.FAILED && response.msg) {
        feedback.msgError(response.msg);
      }
    };
    const handleError = (event: any, file: any) => {
      feedback.msgError(`${file.name}文件上传失败`);
      uploadRefs.value?.abort(file);
      visible.value = false;
      emit("error");
    };
    const handleExceed = () => {
      feedback.msgError(`超出上传上限${props.limit}，请重新上传`);
    };
    const handleClose = () => {
      if (!props.showFileList) uploadRefs.value?.clearFiles();
      visible.value = false;
    };
    const handleRemove = (uploadFile: any, uploadFiles: any) => {
      emit("success", uploadFile, uploadFiles);
    };

    const getAccept = computed(() => {
      return ".jpg,.png,.gif,.jpeg,.ico,.bmp,.wmv,.avi,.mov,.mp4,.flv";
    });
    return {
      uploadRefs,
      action,
      headers,
      visible,
      fileList,
      getAccept,
      handleProgress,
      handleSuccess,
      handleError,
      handleExceed,
      handleClose,
      handleRemove,
    };
  },
});
</script>

<style lang="scss"></style>
