import re
from typing import Dict, Any

import arrow
from bson import ObjectId
from fastapi.responses import StreamingResponse

from apps.db import MongoDB, InfluxDB
from apps.models.common import ObjectIdStr
import apps.models.data_man as DModel
from apps.common import HttpResp, AppException


COLL_VIDEO = MongoDB.get_collection("video_data")
COLL_VEHICLE = MongoDB.get_collection("vehicles")


async def vehicle_list():
    """获取车辆列表"""
    results = COLL_VEHICLE.find({}, {"_id": 1, "vehicle_name": 1, "vehicle_type": 1, "description": 1})
    vehicle_list = []
    async for result in results:
        vehicle_list.append(result)
    return vehicle_list


async def query_video_data(q: DModel.VideoQuery):
    """查询视频分段数据"""
    filter_d: Dict[Any, Any] = {}
    if q.vehicle_id:
        filter_d["vehicle_id"] = q.vehicle_id
    if q.stream_name:
        filter_d["stream_name"] = q.stream_name

    if q.start_time and q.end_time:
        filter_d["segment_start_time"] = {"$gte": q.start_time, "$lte": q.end_time}

    order = {"segment_start_time": 1}
    if q.time_order:
        order["segment_start_time"] = q.time_order

    video_count = await COLL_VIDEO.count_documents(filter_d)
    if video_count == 0:
        return {"count": 0, "lists": []}

    limit = q.page_size
    skip_no = (q.page_no - 1) * q.page_size

    video_list = []
    results = COLL_VIDEO.find(filter_d).sort(order).skip(skip_no).limit(limit)
    async for result in results:
        _data = DModel.VideoListOut(**result)
        video_list.append(_data.model_dump())

    return {"count": video_count, "lists": video_list}


async def get_video_data_by_id(segment_id: ObjectIdStr) -> DModel.VideoListOut:
    """根据视频数据ID获取视频数据"""
    video_data = await COLL_VIDEO.find_one({"_id": ObjectId(segment_id)})
    if video_data is None:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "video info not found")
    video = DModel.VideoListOut(**video_data)
    return video


async def get_video_segment_data(video_id: ObjectIdStr, range: str) -> StreamingResponse:
    video = await get_video_data_by_id(video_id)
    start = 0
    end = video.size - 1
    if range:
        range = range.strip().lower()
        range_match = re.match(r"bytes=(\d+)-(\d+)?", range)
        if range_match:
            start = int(range_match.group(1))
            if range_match.group(2):
                end = int(range_match.group(2))

    content_length = end - start + 1
    headers = {
        "Content-Range": f"bytes {start}-{end}/{video.size}",
        "Accept-Ranges": "bytes",
        "Content-Length": str(content_length),
        "Content-Type": "video/mp4",
    }
    if not video.exists:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "video segment data not found.")

    return StreamingResponse(video.iterfile(start, end), headers=headers, status_code=206)


async def get_joystick_data(vehicle_id: ObjectId, q: DModel.TimeRangeQuery) -> dict:
    """获取车辆的摇杆数据信息"""
    start_time = arrow.get(q.start_time)
    end_time = arrow.get(q.end_time)
    if start_time >= end_time:
        raise AppException(HttpResp.TIME_RANGE_ERROR)

    # 限制查询时间最大为十分钟
    diff_time = end_time - start_time
    if diff_time.seconds > 600:
        end_time = start_time.shift(seconds=600)

    query = f"""from(bucket: "oms")
        |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
        |> filter(fn: (r) => r["_measurement"] == "control_data")
        |> filter(fn: (r) => \
            r["_field"] == "left_joystick_x" or \
            r["_field"] == "left_joystick_y" or \
            r["_field"] == "right_joystick_x" or \
            r["_field"] == "right_joystick_y" or \
            r["_field"] == "left_pedal" or \
            r["_field"] == "right_pedal")
        |> filter(fn: (r) => r["vehicle_id"] == "{str(vehicle_id)}")
        |> aggregateWindow(every: 1s, fn: last, createEmpty: false)
        |> yield(name: "mean")
    """
    result = await InfluxDB().query_stream(query)
    data: Dict[int, Any] = {}
    async for record in result:
        time_key = int(record.get_time().timestamp())
        try:
            data[time_key]
        except KeyError:
            data[time_key] = {}
        data[time_key].update({record.get_field(): record.get_value()})
    return data
