"""
    作业指导管理增删改查
"""

from typing import Annotated
from fastapi import APIRouter, Request, Query, Header
from apps.common import unified_resp, HttpResp, AppException
import apps.services.guide_man as GuideService
import apps.models.guide_man as GuideModel


router = APIRouter(prefix="/guide", tags=["作业指导管理"])


@router.post("/{guide_id}/item", tags=["作业指导管理"])
@unified_resp
async def guide_item_create(_: Request, guide_id: str, item: GuideModel.GuideItemCreate):
    return await GuideService.guide_item_create(guide_id, item)


@router.put("/{guide_id}/item/{item_id}", tags=["作业指导管理"])
@unified_resp
async def guide_item_update(_: Request, guide_id: str, item_id: int, item: GuideModel.GuideItemUpdate):
    return await GuideService.guide_item_update(guide_id, item_id, item)

@router.delete("/{guide_id}/item/{item_id}", tags=["作业指导管理"])
@unified_resp
async def guide_item_delete(_: Request, guide_id: str, item_id: int):
    return await GuideService.guide_item_delete(guide_id, item_id)

@router.post("/", tags=["作业指导管理"])
@unified_resp
async def guide_create(_: Request, guide: GuideModel.GuideCreate):
    return await GuideService.guide_create(guide)


@router.get("/", tags=["作业指导管理"])
@unified_resp
async def guide_list(_: Request, q: Annotated[GuideModel.GuideQuery, Query()]):
    return await GuideService.guide_list(q)


@router.put("/{guide_id}", tags=["作业指导管理"])
@unified_resp
async def guide_update(_: Request, guide_id: str, guide: GuideModel.GuideCreate):
    return await GuideService.guide_update(guide_id, guide)


@router.delete("/{guide_id}", tags=["作业指导管理"])
@unified_resp
async def guide_delete(_: Request, guide_id: str):
    return await GuideService.guide_delete(guide_id)


@router.get("/{guide_id}", tags=["作业指导管理"])
@unified_resp
async def guide_detail(_: Request, guide_id: str):
    return await GuideService.guide_detail(guide_id)
