<template>
  <popup
    custom-class="no-footer"
    ref="popupRef"
    title="日志详情"
    :async="false"
    width="70%"
    @confirm="handleSubmit"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" label-width="100px">
      <el-form-item v-for="(value, key, index) in formData" :key="key" :label="key" :prop="value">
        <p style="word-break: break-all; width: 100%">{{ value }}</p>
      </el-form-item>
    </el-form>
  </popup>
</template>

<script lang="ts" setup>
import Popup from "@/components/popup/index.vue";

const emit = defineEmits(["success", "close"]);
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const props = defineProps({
  formData: {
    type: Object,
    require: true,
  },
});

const open = () => {
  popupRef.value?.open();
};

const handleClose = () => {
  emit("close");
};

const handleSubmit = () => {
  emit("success");
  handleClose();
};

onMounted(() => {});

onUnmounted(() => {});

defineExpose({
  open,
});
</script>

<style scoped></style>
