<template>
  <div class="edit-popup">
    <el-drawer v-model="drawerShow" :title="title" :size="650">
      <div class="h-full flex flex-col">
        <JsonEditorVue
          v-if="drawerShow"
          class="w-[580px] ml-4"
          style="height: calc(100% - 50px)"
          v-model="params"
          mode="text"
          :mainMenuBar="false"
          :onChange="onJsonSave"
        />
        <div class="flex flex-grow justify-end items-end mt-4 h-8">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import feedback from "@/utils/feedback";
import JsonEditorVue from "json-editor-vue";

const props = defineProps({
  title: {
    type: String,
    require: true,
    default: "JSON参数编辑",
  },
  jsonParams: {
    type: Object,
    require: true,
    default: {},
  },
});

const emit = defineEmits(["success", "close"]);
const drawerShow = ref(false);
const params: any = ref({});
const objParams: any = ref({});

const onJsonSave = (value: any) => {
  objParams.value = JSON.parse(value.text);
};

const handleSubmit = async () => {
  emit("success", objParams);
  handleClose();
};

const open = () => {
  drawerShow.value = true;
  objParams.value = props.jsonParams;
  params.value = props.jsonParams;
};

const handleClose = () => {
  params.value = {};
  objParams.value = {};
  drawerShow.value = false;
};

defineExpose({
  open,
});
</script>
