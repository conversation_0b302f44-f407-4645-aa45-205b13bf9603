FROM node:20-alpine AS build-stage

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /app

# 拷贝 package.json 和 package-lock.json (如果存在)
COPY package*.json ./

# 设置 node 阿里镜像并安装依赖
RUN npm config set registry https://registry.npmmirror.com/ && npm install

# 拷贝所有代码
COPY . .

# 打包
RUN npm run build

# Stage 2: Serve the application with Nginx
FROM nginx:1.26.2-alpine

# 移除默认的 Nginx 配置
RUN rm /etc/nginx/conf.d/default.conf

# 从构建阶段拷贝打包好的文件到 Nginx
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 拷贝自定义的 Nginx 配置
COPY nginx/default.conf /etc/nginx/conf.d/

# 暴露端口
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
