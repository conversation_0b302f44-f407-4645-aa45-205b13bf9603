from typing import Union, List, Dict, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from fastapi import UploadFile
from .common import PagingModel, ObjectIdStr


class MaterialCateOut(BaseModel):
    id: int = Field(..., alias="_id")
    type: int = Field(..., description="类型", title="类型", example=1)
    name: str = Field(..., description="名称", title="名称", example="名称")
    create_time: datetime = Field(..., description="创建时间", title="创建时间", example="2025-01-01 00:00:00")
    update_time: datetime = Field(..., description="更新时间", title="更新时间", example="2025-01-01 00:00:00")


class MaterialCate(BaseModel):
    type: int = Field(..., description="类型", title="类型", example=1)
    name: str = Field(..., description="名称", title="名称", example="名称")


class MaterialCateEdit(BaseModel):
    name: str = Field(..., description="名称", title="名称", example="名称")


class MaterialUpload(BaseModel):
    cid: int = Field(..., description="分类ID", title="分类ID", example=1)
    file: UploadFile = Field(..., description="文件", title="文件")


class MaterialQuery(PagingModel):
    cid: Optional[int] = None
    type: Optional[int] = None


class MaterialOut(BaseModel):
    id: ObjectIdStr = Field(..., alias="_id")
    type: int = Field(..., description="类型", title="类型", example=1)
    name: str = Field(..., description="名称", title="名称", example="名称")
    url: str = Field(..., description="URL", title="URL", example="URL")
    size: int = Field(..., description="大小", title="大小", example=1)
    ext: str = Field(..., description="后缀", title="后缀", example="后缀")
    thumbnail: str = Field(..., description="缩略图", title="缩略图", example="缩略图")
    create_time: Optional[datetime] = None
