<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="550px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="80px" :rules="formRules">
        <el-form-item label="父级现象" prop="pid">
          <el-tree-select
            class="flex-1"
            v-model="formData.pid"
            :data="menuOptions"
            clearable
            node-key="id"
            :props="{
              label: 'content',
            }"
            :default-expand-all="true"
            placeholder="请选择父级现象"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="现象描述" prop="content">
          <el-input v-model="formData.content" placeholder="请输入现象描述" clearable />
        </el-form-item>
        <el-form-item label="操作详情" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            placeholder="请输入操作详情"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="操作选择" prop="operation_id">
          <el-select v-model="formData.operation_id" multiple collapse-tags placeholder="Select">
            <el-option v-for="item in answer_options" :key="item.id" :label="item.content" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="现象排序" prop="sort">
          <div>
            <el-input-number v-model="formData.sort" :max="9999" />
            <div class="form-tips">数值越大越排前</div>
          </div>
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import { phenomenonList, phenomenonAdd, operationList, phenomenonEdit } from "@/api/customer-ja";

import { getModulesKey } from "@/router";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import { arrayToTree, treeToArray } from "@/utils/util";

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑现象" : "新增现象";
});

const componentsOptions = ref(getModulesKey());
const querySearch = (queryString: string, cb: any) => {
  const results = queryString
    ? componentsOptions.value.filter((item) => item.toLowerCase().includes(queryString.toLowerCase()))
    : componentsOptions.value;
  cb(results.map((item) => ({ value: item })));
};

const formData = reactive({
  id: "",
  pid: "0",
  sort: 0,
  content: "",
  description: "",
  operation_id: [],
});

const formRules = {
  pid: [
    {
      required: true,
      message: "请选择父级现象",
      trigger: ["blur", "change"],
    },
  ],
  content: [
    {
      required: true,
      message: "请输入现象描述",
      trigger: "blur",
    },
  ],
  operation_id: [
    {
      required: false,
      message: "请选择操作",
      trigger: "blur",
    },
  ],
};
const menuOptions = ref<any[]>([]);
const answer_options = ref<any[]>([]);

const getPhenomenon = async () => {
  const { lists } = await operationList({page_no:1,page_size:99});
  answer_options.value = lists;

  const data: any = await phenomenonList({});
  const menu: any = { id: "0", content: "顶级", children: [] };
  menu.children = arrayToTree(treeToArray(data), { id: "_id", parentId: "pid", children: "children" });
  menuOptions.value.push(menu);
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit" ? await phenomenonEdit(formData) : await phenomenonAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = (data: Record<any, any>) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      //@ts-ignore
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

getPhenomenon();

defineExpose({
  open,
  setFormData,
});
</script>
