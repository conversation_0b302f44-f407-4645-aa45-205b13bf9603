<template>
  <div>
    <el-card class="!border-none" shadow="never">
      <el-form ref="formRef" @submit.native.prevent class="mb-[-16px]" :model="queryParams" :inline="true">
        <el-form-item label="顧客情報">
          <el-input
            class="w-[200px]"
            v-model="queryParams.keyword"
            placeholder="顧客名/連絡先"
            clearable
            @keyup.enter="resetPage"
          />
        </el-form-item>

        <el-form-item label="現象">
          <el-input
            class="w-[200px]"
            v-model="queryParams.phenomenon"
            placeholder="キーワードを入力してください"
            clearable
            @keyup.enter="resetPage"
          />
        </el-form-item>

        <el-form-item label="ステータス">
          <el-select class="!w-36" v-model="queryParams.status" placeholder="選択してください">
            <el-option v-for="(item, key) in SessionStatus" :key="key" :label="item" :value="Number(key)" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="resetPage">検索</el-button>
          <el-button @click="resetParams">リセット</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="!border-none mt-4" shadow="never">
      <el-table :height="calcTableHeight()" size="large" v-loading="pager.loading" :data="pager.lists">
        <el-table-column label="ID" prop="id" min-width="100" />
        <el-table-column label="お客様名" prop="customer_name" min-width="100" />
        <el-table-column label="担当者名" prop="person_name" min-width="100" />
        <el-table-column label="連絡先" prop="contact_info" min-width="100" />
        <el-table-column label="操作台" prop="device_control_name" />
        <el-table-column label="重機側" prop="device_vehicle_name" />
        <el-table-column label="ステータス" prop="status" min-width="100">
          <template #default="{ row }">
            <el-tag :type="TagStatus[row.status]">{{ SessionStatus[row.status] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="現象" prop="phenomenon_id" min-width="100">
          <template #default="{ row }">
            {{ phenomenonFind(row.phenomenon_id) || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="作成日時" prop="create_time" min-width="120" >
          <template #default="{ row }">
            {{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column label="解決日時" prop="completion_time" min-width="120" >
          <template #default="{ row }">
            {{ row.completion_time ? dayjs(row.completion_time).local().format("YYYY-MM-DD HH:mm:ss") : "--" }}
          </template>
        </el-table-column>
        <el-table-column label="詳細情報" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleDetail(row)"> 詳細 </el-button>
            <el-button type="danger" link @click="handleDelete(row.id)"> 削除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex justify-end mt-4">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>
<script lang="ts" setup name="consumerLists">
import { usePaging } from "@/hooks/usePaging";
import { orderList, orderDelete } from "@/api/customer-ja";
import EditPopup from "./edit.vue";
import feedback from "@/utils/feedback";
import { storeToRefs } from "pinia";
import useCustomerJaStore from "@/stores/modules/customer-ja";
const customerStore = useCustomerJaStore();
const { phenomenonList, operationList } = storeToRefs(customerStore);

const SessionStatus: any = {
  1: "未対応",
  2: "対応中",
  3: "解決済",
  4: "BX対応中",
};
const TagStatus: any = {
  1: "info",
  2: "warning",
  3: "success",
  4: "danger",
};

const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const showEdit = ref(false);
const queryParams = reactive({
  keyword: "",
  phenomenon: "",
  status: "",
});

const { pager, getLists, resetPage, resetParams } = usePaging({
  fetchFun: orderList,
  params: queryParams,
});

const calcTableHeight = () => {
  return window.innerHeight - 260;
};

const phenomenonFind = (ids: [number]) => {
  let arr: any = [];
  phenomenonList.value.forEach((item: any) => {
    item.children.forEach((i: any) => {
      if (ids.includes(i.id)) {
        arr.push(i.content);
      }
    });
  });

  return arr.join("，");
};

onMounted(async () => {
  await getLists();
  await customerStore.getPhenomenonList();
  await customerStore.getOperationList();
});

const handleDetail = async (row: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("detail");
  editRef.value?.setFormData(row);
};

const handleDelete = async (id: any) => {
  await feedback.confirm("本当に削除してよろしいでしょうか？");
  await orderDelete(id);
  getLists();
};

const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};
</script>
