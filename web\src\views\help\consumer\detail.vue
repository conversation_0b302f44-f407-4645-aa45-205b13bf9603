<template>
  <div class="">
    <div class="head h-[48px] bg-white py-2 px-2 rounded-[4px]">
      <el-page-header @back="() => router.back()">
        <template #content>
          <div class="flex items-center">
            <span class="text-large font-600 mr-3"> {{ projectDetail.customer_name }} </span>
          </div>
        </template>
        <template #extra>
          <div class="flex items-center">
            <el-button v-if="!pageData.idEdit" type="info" @click="changeType">編集</el-button>
            <el-button v-else type="primary" @click="changeType">保存</el-button>
          </div>
        </template>
      </el-page-header>
    </div>
    <div class="mt-2 h-[calc(100vh-48px)] rounded-[4px]">
      <div class="flex flex-row base-map-timeline-wrap">
        <div class="base-map-wrap flex">
          <div class="base-wrap project-item bg-white rounded-[4px] h-[164px]">
            <div class="title">
              <div>基本情報</div>
              <div>
                <el-button
                  v-if="pageData.idEdit"
                  type="primary"
                  size="small"
                  @click="showBaseEditDialog"
                >
                  編集
                </el-button>
              </div>
            </div>
            <BaseShow
              :devices_info="projectDetail.devices_info"
              :customer_name="projectDetail.customer_name"
              :contact_info="projectDetail.contact_info"
              :person_name="projectDetail.person_name"
              :create_time="projectDetail.create_time"
            ></BaseShow>
          </div>
        </div>
      </div>
      <div class="flex flex-col device-wrap mt-[10px]">
        <div class="vehicle-wrap bg-white rounded-[4px] p-2">
          <div class="title">
            <div>重機側情報</div>
            <div>
              <el-button
                v-if="pageData.idEdit"
                type="primary"
                size="small"
                @click="showVehicleEditDialog"
                >新規追加</el-button
              >
            </div>
          </div>
          <div class="content pt-2">
            <VehiccleItem
              v-for="item in projectDetail.vehicle"
              :vehicle_id="item.vehicle_id"
              :vehicle_name="item.vehicle_name"
              :vehicle_type="item.vehicle_type"
              :vehicle_sn="item.vehicle_sn"
              :vehicle_address="item.vehicle_address"
              :install_date="item.install_date"
              :delivery_date="item.delivery_date"
              :warranty_end_date="item.warranty_end_date"
              :smart_feature="item.smart_feature"
              @edit="handleVehicleEdit"
              @delete="handleVehicleDelete"
            ></VehiccleItem>
            <el-empty class="mx-auto" v-if="projectDetail.vehicle.length === 0" />
          </div>
        </div>
        <div class="console-wrap bg-white rounded-[4px] h-[354px] p-2 mt-[10px]">
          <div class="title">
            <div>操作台側情報</div>
            <div>
              <el-button
                v-if="pageData.idEdit"
                type="primary"
                size="small"
                @click="showConsoleEditDialog"
              >
                新規追加
              </el-button>
            </div>
          </div>
          <div class="content pt-2">
            <ConsoleItem
              v-for="item in projectDetail.console"
              :console_id="item.console_id"
              :console_name="item.console_name"
              :console_type="item.console_type"
              :console_sn="item.console_sn"
              :console_address="item.console_address"
              :smart_feature="item.smart_feature"
              :install_date="item.install_date"
              :delivery_date="item.delivery_date"
              :warranty_end_date="item.warranty_end_date"
              @edit="handleConsoleEdit"
              @delete="handleConsoleDelete"
            ></ConsoleItem>
            <el-empty class="mx-auto" v-if="projectDetail.console.length === 0" />
          </div>
        </div>
      </div>
      <div class="bg-white rounded-[4px] h-[380px] p-2 mt-[10px]">
        <div class="title">
          <div>故障診断一覧</div>
        </div>
        <div class="pt-2">
          <el-table :data="projectDetail.work_order" :height="320">
            <el-table-column label="ID" prop="id" min-width="100" />
            <el-table-column label="お客様名" prop="customer_name" min-width="100" />
            <el-table-column label="担当者名" prop="person_name" min-width="100" />
            <el-table-column label="連絡先" prop="contact_info" min-width="100" />
            <el-table-column label="操作台" prop="device_control_name" />
            <el-table-column label="重機側" prop="device_vehicle_name" />
            <el-table-column label="ステータス" prop="status" min-width="100">
              <template #default="{ row }">
                <el-tag :type="TagStatus[row.status]">{{ SessionStatus[row.status] }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="現象" prop="phenomenon_id" min-width="100">
              <template #default="{ row }">
                {{ phenomenonFind(row.phenomenon_id) || "--" }}
              </template>
            </el-table-column>
            <el-table-column label="作成日時" prop="create_time" min-width="120">
              <template #default="{ row }">
                {{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </el-table-column>
            <el-table-column label="解決日時" prop="completion_time" min-width="120">
              <template #default="{ row }">
                {{
                  row.completion_time
                    ? dayjs(row.completion_time).local().format("YYYY-MM-DD HH:mm:ss")
                    : "--"
                }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <BaseEditDialog
      ref="baseEditRef"
      v-if="pageData.idShowBaseEditDialog"
      @success="handleBaseEdit"
      @close="pageData.idShowBaseEditDialog = false"
    ></BaseEditDialog>

    <VehicleEditDialog
      :id="projectDetail.id"
      ref="vehicleEditDialogRef"
      v-if="pageData.isShowVehicleEditDialog"
      @success="getProjectDetail()"
      @close="pageData.isShowVehicleEditDialog = false"
    ></VehicleEditDialog>
    <ConsoleEditDialog
      :id="projectDetail.id"
      ref="consoleEditDialogRef"
      v-if="pageData.isShowConsoleEditDialog"
      @success="getProjectDetail()"
    ></ConsoleEditDialog>
  </div>
</template>

<script setup lang="ts">
import feedback from "@/utils/feedback";

import BaseShow from "./components/base-show.vue";
import VehiccleItem from "./components/vehicle-item.vue";
import ConsoleItem from "./components/console-item.vue";
import BaseEditDialog from "./components/base-edit.vue";
import VehicleEditDialog from "./components/vehicle-edit.vue";
import ConsoleEditDialog from "./components/console-edit.vue";

import {
  projectDetail as projectDetailApi,
  projectBaseEdit,
  projectDeleteVehicle,
  projectDeleteConsole,
} from "@/api/project-man";
import { orderList } from "@/api/customer-ja";
import { storeToRefs } from "pinia";
import useCustomerJaStore from "@/stores/modules/customer-ja";
const customerStore = useCustomerJaStore();
const { phenomenonList } = storeToRefs(customerStore);

const router = useRouter();
const route = useRoute();
const baseEditRef = shallowRef<InstanceType<typeof BaseEditDialog>>();
const vehicleEditDialogRef = shallowRef<InstanceType<typeof VehicleEditDialog>>();
const consoleEditDialogRef = shallowRef<InstanceType<typeof ConsoleEditDialog>>();

const pageData = reactive({
  idEdit: false,
  idShowBaseEditDialog: false,
  isShowVehicleEditDialog: false,
  isShowConsoleEditDialog: false,
});

const projectDetail = reactive({
  id: "",
  customer_name: "", // 客户名称
  contact_info: "", // 联系方式
  person_name: "", // 名称
  devices_info: "", // 设备信息
  create_time: "", // 创建时间
  prj_progress: [
    {
      content: "项目创建",
      timestamp: "2025-01-01 00:01",
      progress_type: "primary",
      hollow: true,
      color: "#0bbd87",
      size: "large",
    },
  ], // 项目时间线，项目的生命周期，从开始到完成
  vehicle: [
    {
      vehicle_id: "",
      vehicle_name: "",
      vehicle_type: 1,
      vehicle_sn: "",
      vehicle_address: "",
      smart_feature: [],
      install_date: "",
      delivery_date: "",
      warranty_end_date: "",
    },
  ],
  console: [
    {
      console_id: "",
      console_name: "",
      console_type: 1,
      console_sn: "",
      console_address: "",
      smart_feature: [],
      install_date: "",
      delivery_date: "",
      warranty_end_date: "",
    },
  ],
  work_order: [],
});

const changeType = () => {
  pageData.idEdit = !pageData.idEdit;
};

const showBaseEditDialog = async () => {
  pageData.idShowBaseEditDialog = true;
  await nextTick();
  baseEditRef.value?.setFormData(projectDetail);
  baseEditRef.value?.open();
};

const showVehicleEditDialog = async () => {
  pageData.isShowVehicleEditDialog = true;
  await nextTick();
  vehicleEditDialogRef.value?.open();
};

const showConsoleEditDialog = async () => {
  pageData.isShowConsoleEditDialog = true;
  await nextTick();
  consoleEditDialogRef.value?.open();
};

const handleBaseEdit = async (data: any) => {
  Object.assign(projectDetail, data);
  await projectBaseEdit(data);
  pageData.idShowBaseEditDialog = false;
};

const handleVehicleEdit = async (id: any) => {
  pageData.isShowVehicleEditDialog = true;
  await nextTick();
  vehicleEditDialogRef.value?.open("edit");
  const targetVehicle = projectDetail.vehicle.find((item: any) => item.vehicle_id == id);
  vehicleEditDialogRef.value?.setFormData(targetVehicle);
};

const handleVehicleDelete = async (vehicle_id: string) => {
  await feedback.confirm("本当に削除してよろしいでしょうか？");
  await projectDeleteVehicle({
    id: projectDetail.id,
    vehicle_id: vehicle_id,
  });
  feedback.msgSuccess("削除に成功しました");
  getProjectDetail(projectDetail.id);
};

const handleConsoleEdit = async (id: any) => {
  pageData.isShowConsoleEditDialog = true;
  await nextTick();
  consoleEditDialogRef.value?.open("edit");
  const targetConsole = projectDetail.console.find((item: any) => item.console_id == id);
  consoleEditDialogRef.value?.setFormData(targetConsole);
};

const handleConsoleDelete = async (console_id: string) => {
  await feedback.confirm("本当に削除してよろしいでしょうか？");
  await projectDeleteConsole({
    id: projectDetail.id,
    console_id: console_id,
  });
  feedback.msgSuccess("削除に成功しました");
  getProjectDetail(projectDetail.id);
};

const handleSave = () => {
  console.log("handleSave");
};

const getProjectDetail = async (id: string = projectDetail.id) => {
  const data = await projectDetailApi(id);
  Object.assign(projectDetail, data);
};

const SessionStatus: any = {
  1: "未対応",
  2: "対応中",
  3: "解決済",
  4: "BX対応中",
};
const TagStatus: any = {
  1: "info",
  2: "warning",
  3: "success",
  4: "danger",
};

const phenomenonFind = (ids: [number]) => {
  let arr: any = [];
  phenomenonList.value.forEach((item: any) => {
    item.children.forEach((i: any) => {
      if (ids.includes(i.id)) {
        arr.push(i.content);
      }
    });
  });

  return arr.join("，");
};

onMounted(async () => {
  const { id, type } = route.query;

  if (type === "edit") {
    pageData.idEdit = true;
  }

  if (id) {
    getProjectDetail(id as string);
    const { lists } = await orderList({ customer_id: id as string, page_size: 100 });
    projectDetail.work_order = lists;
  }
});
</script>

<style scoped lang="scss">
.project-item {
  padding: 8px;
}

.title {
  border-left: 2px solid var(--el-color-primary);
  padding-left: 6px;
  font-size: 16px;
  font-weight: 500;
  height: 26px;
  line-height: 26px;
  display: flex;
  justify-content: space-between;
}

.vehicle-wrap {
  height: 374px;
  .content {
    display: flex;
    flex-direction: row;
    height: 330px;
    overflow-x: auto;
    overflow-y: hidden;
  }
}

.console-wrap {
  height: 334px;
  .content {
    display: flex;
    flex-direction: row;
    height: 292px;
    overflow-x: auto;
    overflow-y: hidden;
  }
}

@media screen and (min-width: 1480px) {
  .base-map-wrap {
    flex: 2;
  }
  .base-map-wrap {
    flex-direction: row;
  }
  .project-item {
    flex: 1;
    margin-left: 10px;
  }
  .project-item:first-child {
    margin-left: 0;
  }
}

@media (min-width: 740px) and (max-width: 1479px) {
  .base-wrap + .map-wrap {
    margin-top: 10px;
  }

  .base-map-wrap {
    flex: 1;
  }
  .base-map-wrap {
    flex-direction: column;
  }
}

@media (max-width: 739px) {
  .base-wrap + .map-wrap {
    margin-top: 10px;
  }
  .base-map-wrap {
    flex-direction: column;
  }
  .base-map-timeline-wrap {
    flex-direction: column;
  }
}
</style>
