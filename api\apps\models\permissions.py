from enum import IntEnum, IntFlag


class FuncId(IntEnum):
    """权限 ID 用于权限校验"""

    DeviceManage = 1000
    DeviceBind = 1001
    DeviceUpdate = 1002
    DeviceDelete = 1003
    DeviceView = 1004

    VehicleManage = 2000
    VehicleCreate = 2001
    VehicleUpdate = 2002
    VehicleDelete = 2003
    VehicleView = 2004

    OpcManage = 3000
    OpcCreate = 3001
    OpcUpdate = 3002
    OpcDelete = 3003
    OpcView = 3004

    UserManage = 4000
    UserCreate = 4001
    UserUpdate = 4002
    UserDelete = 4003
    UserView = 4004

    RoleManage = 5000
    RoleCreate = 5001
    RoleUpdate = 5002
    RoleDelete = 5003
    RoleView = 5004

    MenusManage = 6000
    MenusCreate = 6001
    MenusUpdate = 6002
    MenusDelete = 6003
    MenusView = 6004

    DataManage = 7000
    DataView = 7001

    MetadataManage = 7100

    AlarmView = 8001

    AuditManage = 9000


class RoleResourceFlag(IntFlag):
    """车辆权限类型，查看权限，操作权限，管理员权限"""

    VIEWER = 1
    OPERATOR = 2
    ADMIN = 4
