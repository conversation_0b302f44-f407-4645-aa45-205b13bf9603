<template>
  <div class="admin">
    <el-card class="!border-none" shadow="never">
      <el-form class="mb-[-16px]" :model="formData" inline>
        <el-form-item label="人员账号">
          <el-input v-model="formData.username" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <el-form-item label="人员名称">
          <el-input v-model="formData.nickname" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <el-form-item class="w-[260px]" label="人员角色">
          <el-select v-model="formData.role">
            <el-option label="全部" value="" />
            <el-option
              v-for="(item, index) in optionsData.role.lists"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="pager.loading" class="mt-4 !border-none" shadow="never">
      <el-button type="primary" @click="handleAdd">
        <template #icon>
          <icon name="el-icon-Plus" />
        </template>
        新增
      </el-button>
      <div class="mt-4">
        <el-table :data="pager.lists" size="large">
          <!-- <el-table-column label="ID" prop="id" min-width="60" /> -->
          <!-- <el-table-column label="头像" min-width="100">
            <template #default="{ row }">
              <el-avatar :size="50" :src="row.avatar"></el-avatar>
            </template>
          </el-table-column> -->
          <el-table-column label="账号" prop="username" min-width="100" />
          <el-table-column label="名称" prop="nickname" min-width="100" />
          <el-table-column label="角色" prop="roles" min-width="100">
            <template #default="{ row }">
              <div class="flex items-center">
                <el-tooltip :content="formatRoles(row.roles)" placement="top-start">
                  <div class="ml-[10px] text_hidden">{{ formatRoles(row.roles) }}</div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="create_time" min-width="180">
            <template #default="{ row }">
              {{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </el-table-column>
          <el-table-column label="最近登录时间" prop="last_login_time" min-width="180">
            <template #default="{ row }">
              {{ row.last_login_time ? dayjs(row.last_login_time).local().format("YYYY-MM-DD HH:mm:ss") : "--" }}
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template #default="{ row }">
              <el-switch
                v-if="row.id != 1"
                :model-value="row.status"
                :active-value="0"
                :inactive-value="1"
                @change="changeStatus($event, row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="190" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
              <el-button type="primary" link @click="handleResetPassword(row)"> 重置密码 </el-button>
              <el-button v-if="row.id != 1" type="danger" link @click="handleDelete(row.id)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>

<script lang="ts" setup name="admin">
import useClipboard from "vue-clipboard3";
import { adminLists, adminDelete, adminStatus, adminResetRequest } from "@/api/perms/admin";
import { roleAll } from "@/api/perms/role";
import { useDictOptions } from "@/hooks/useDictOptions";
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
// 表单数据
const formData = reactive({
  username: "",
  nickname: "",
  role: "",
});
const showEdit = ref(false);
const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: adminLists,
  params: formData,
});

const formatRoles = (roles: any[]) => {
  if (!roles || !roles.length) return "--";
  return roles.map(({ name }) => name).join(",");
};

const changeStatus = async (active: any, id: number) => {
  try {
    await feedback.confirm(`确定${active ? "停用" : "开启"}当前管理员？`);
    await adminStatus({ status: active, id });
    feedback.msgSuccess("修改成功");
    getLists();
  } catch (error) {
    getLists();
  }
};
const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

const handleResetPassword = async (data: any) => {
  console.log(data);
  
  await feedback.confirm("确定要重置密码？");
  const { token } = await adminResetRequest({ id: data.id });
  const host = window.location.host;
  const url = `http://${host}/reset?token=${token}`;
  const { toClipboard } = useClipboard();
  toClipboard(url)
    .then(() => {
      feedback.msgSuccess(`${data.nickname}的密码重置链接已复制到剪切板`);
    })
    .catch(() => {
      feedback.msgError("复制失败");
    });
};

const handleDelete = async (id: number) => {
  await feedback.confirm("确定要删除？");
  await adminDelete({ uid: id });
  feedback.msgSuccess("删除成功");
  getLists();
};

const { optionsData } = useDictOptions<{
  role: {
    lists: any[];
  };
}>({
  role: {
    api: roleAll,
  },
});

onMounted(() => {
  getLists();
});
</script>
<style lang="scss" scoped>
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
