"""
    权限管理_菜单增查改删测试
"""

import pytest


@pytest.mark.dependency()
def test_add_menu(client, mdb, admin_headers):
    """ 新增菜单 """
    data = {
        "id": "",
        "pid": 0,
        "menu_type": "C",
        "menu_icon": "el-icon-Monitor",
        "menu_name": "pytest_menu",
        "menu_sort": 100,
        "paths": "workbench",
        "perms": "index:console",
        "component": "workbench/index",
        "selected": "",
        "params": "",
        "is_cache": 1,
        "is_show": 1,
        "is_disable": 0
    }

    res = client.post("/user/menu", json=data, headers=admin_headers)
    assert res.json()['code'] == 200
    menu_data = mdb['menus'].find_one({"menu_name": "pytest_menu"})
    assert menu_data is not None


@pytest.mark.dependency()
def test_query_menu(client, mdb, admin_headers):
    """ 查询菜单 """
    res = client.get("/user/menu", headers=admin_headers)
    res_data = res.json()
    assert res_data['code'] == 200
    assert len(res_data['data']) > 0


@pytest.mark.dependency(depends=["test_add_menu"])
def test_update_menu(client, mdb, admin_headers):
    """ 更新菜单 """
    menu_data = mdb['menus'].find_one({"menu_name": "pytest_menu"})
    assert menu_data is not None
    mid = str(menu_data['_id'])
    data = {
        "id": mid,
        "pid": 0,
        "menu_type": "C",
        "menu_icon": "el-icon-Monitor",
        "menu_name": "pytest_menu_1",
        "menu_sort": 100,
        "paths": "workbench",
        "perms": "index:console",
        "component": "workbench/index",
        "selected": "",
        "params": "",
        "is_cache": 1,
        "is_show": 1,
        "is_disable": 0
    }
    res = client.put("/user/menu", json=data, headers=admin_headers)
    assert res.json()['code'] == 200
    menu_data = mdb['menus'].find_one({"menu_name": "pytest_menu_1"})
    assert menu_data is not None


@pytest.mark.dependency(depends=["test_update_menu"])
def test_detail_menu(client, mdb, admin_headers):
    """ 查询菜单详情 """
    menu_data = mdb['menus'].find_one({"menu_name": "pytest_menu_1"})
    assert menu_data is not None
    mid = str(menu_data['_id'])
    res = client.get("/user/menu/detail", headers=admin_headers, params={"id_": mid})
    res_data = res.json()
    assert res_data['code'] == 200
    assert res_data['data']['menu_name'] == "pytest_menu_1"


@pytest.mark.dependency()
def test_import_menu(client, mdb, admin_headers):
    """ 导入菜单 """
    res = client.get("/user/menu/export", headers=admin_headers)
    res_data = res.json()
    assert res_data['code'] == 200
    assert res_data['data'] is not None
    assert len(res_data['data']) > 0
    import_data = res_data['data']
    res = client.post("/user/menu/import", json=import_data, headers=admin_headers)
    res_data = res.json()
    assert res_data['code'] == 200
    assert res_data['data'] is not None
    assert len(res_data['data']) > 0
    menu_data = mdb['menus'].find_one({"menu_name": "pytest_menu_1"})
    assert menu_data is not None


@pytest.mark.dependency(depends=["test_import_menu"])
def test_delete_menu(client, mdb, admin_headers):
    """ 删除菜单 """
    menu_data = mdb['menus'].find_one({"menu_name": "pytest_menu_1"})
    assert menu_data is not None
    mid = str(menu_data['_id'])
    res = client.delete("/user/menu", headers=admin_headers, params={"id_": mid})
    assert res.json()['code'] == 200
    menu_data = mdb['menus'].find_one({"menu_name": "pytest_menu_1"})
    assert menu_data is None
