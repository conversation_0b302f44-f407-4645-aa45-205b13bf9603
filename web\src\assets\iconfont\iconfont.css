@font-face {
  font-family: "iconfont"; /* Project id 4269297 */
  src: url('iconfont.woff2?t=1704867006136') format('woff2'),
       url('iconfont.woff?t=1704867006136') format('woff'),
       url('iconfont.ttf?t=1704867006136') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-yuyan:before {
  content: "\e618";
}

.icon-bianji:before {
  content: "\e72f";
}

.icon-chakan:before {
  content: "\e72d";
}

.icon-shanchu:before {
  content: "\e735";
}

.icon-ros:before {
  content: "\e6f7";
}

.icon-xingren:before {
  content: "\e60e";
}

