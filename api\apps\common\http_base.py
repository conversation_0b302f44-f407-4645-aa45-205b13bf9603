import json
import inspect
from collections import namedtuple
from datetime import datetime
from functools import wraps
from typing import Callable, TypeVar

import arrow
from bson import ObjectId
from fastapi import Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import J<PERSON>NResponse

from apps.utils import NameStyleConverter as NSC


__all__ = ["HttpCode", "HttpResp", "unified_resp"]

RT = TypeVar("RT")  # 返回类型
HttpCode = namedtuple("HttpCode", ["code", "msg"])


class HttpResp:
    """HTTP响应结果"""

    SUCCESS = HttpCode(200, "success")
    FAILED = HttpCode(300, "failed")

    PARAMS_VALID_ERROR = HttpCode(310, "params valid error")
    TIME_FORMAT_ERROR = HttpCode(310, "time format error")
    PARAMS_TYPE_ERROR = HttpCode(311, "params type error")
    REQUEST_METHOD_ERROR = HttpCode(312, "request method error")
    ASSERT_ARGUMENT_ERROR = HttpCode(313, "assert argument error")
    TIME_RANGE_ERROR = HttpCode(313, "time range error")

    TOKEN_EMPTY = HttpCode(401, "token params empty")
    TOKEN_INVALID = HttpCode(401, "token invalid")
    LOGIN_ACCOUNT_ERROR = HttpCode(401, "user auth fail")
    LOGIN_DISABLE_ERROR = HttpCode(401, "login disable")
    NO_PERMISSION = HttpCode(403, "no permission")
    UNTRUSTED_DEVICE = HttpCode(403, "Untrusted device")
    VEHICLE_LOCKED_BY_OTHER = HttpCode(412, "vehicle locked by other")

    DEVICE_NOT_ONLINE = HttpCode(404, "device not online")
    REQUEST_404_ERROR = HttpCode(404, "request 404 error")
    USER_NOT_EXIST = HttpCode(404, "user not exist")
    ROLE_NOT_EXIST = HttpCode(404, "role not exist")
    MENU_NOT_EXIST = HttpCode(404, "menu not exist")
    VEHICLE_NOT_FOUND = HttpCode(404, "vehicle not found")
    DEVICE_NOT_FOUND = HttpCode(404, "device not found")
    PROGRAM_NODE_NOT_FOUND = HttpCode(404, "program node not found")
    OPC_NOT_FOUND = HttpCode(404, "op console not found")
    METADATA_NOT_FOUND = HttpCode(404, "metadata not found")
    VIDEO_NOT_FOUND = HttpCode(404, "video not found")
    CAMERA_NOT_FOUND = HttpCode(404, "camera device not found")
    TEMPLATE_NOT_FOUND = HttpCode(404, "mix template not found")
    HLS_URL_NOT_FOUND = HttpCode(404, "hls url not found")
    PROJECT_NOT_FOUND = HttpCode(404, "project not found")

    DEVICE_ID_EXISTS = HttpCode(412, "device id exists")
    METADATA_EXISTS = HttpCode(412, "metadata exists")
    PROGRAM_NODE_EXISTS = HttpCode(412, "program node exists")
    ROLE_NAME_REPEAT = HttpCode(412, "role name repeat")
    MENU_NAME_REPEAT = HttpCode(412, "menu name repeat")
    VEHICLE_NAME_REPEAT = HttpCode(412, "vehicle name repeat")
    DEVICE_NAME_REPEAT = HttpCode(412, "device name repeat")
    TEMPLATE_NAME_EXISTS = HttpCode(412, "template name exists")
    GUIDE_EXISTS = HttpCode(412, "guide exists")
    
    # 部门相关错误码
    DEPT_NOT_EXIST = HttpCode(404, "department not exist")
    DEPT_NAME_REPEAT = HttpCode(412, "department name repeat")
    DEPT_HAS_CHILDREN = HttpCode(412, "department has children")
    DEPT_HAS_ROLES = HttpCode(412, "department has roles")
    DEPT_HAS_ROLE_DEPTS = HttpCode(412, "department is referenced by roles")
    DEPT_PARENT_NOT_EXIST = HttpCode(404, "parent department not exist")
    DEPT_PARENT_SELF = HttpCode(412, "parent cannot be self")
    DEPT_PARENT_CHILD = HttpCode(412, "parent cannot be child")
    DEPT_LEVEL_EXCEED = HttpCode(412, "department level exceed limit")

    UNKNOWN_CMD = HttpCode(500, "unknown cmd")
    UNKNOWN_TYPE = HttpCode(500, "unknown type")
    REDIS_ERROR = HttpCode(500, "Redis error")
    SYSTEM_ERROR = HttpCode(500, "system error")
    HLS_ERROR = HttpCode(500, "hls server error")
    SYSTEM_TIMEOUT_ERROR = HttpCode(504, "system timeout error")
    VEHICLE_HAS_DEVICE = HttpCode(500, "vehicle has device")


def unified_resp(func: Callable[..., RT]):
    """统一响应格式
    接口正常返回时,统一响应结果格式
    """

    def datetime_encoder(dt: datetime):
        """日期时间格式化"""
        return arrow.get(dt).isoformat()

    def objectid_encoder(id_: ObjectId):
        """ObjectId格式化"""
        return str(id_)

    def get_request(**kwargs) -> Request:
        """遍历获取 Request"""
        for v in kwargs.values():
            if not isinstance(v, Request):
                continue
            return v
        assert False

    def vriable_naming_rename(method: str, data: str) -> str:
        """变量命名风格转换
        method: 转换方法, snake_case, camelCase, CamelCase
        data: 要转换的数据
        """
        if method == "camelCase":
            return NSC.snake_to_camel(data)
        elif method == "CamelCase":
            return NSC.snake_to_camel2(data)
        return data

    @wraps(func)
    async def wrapper(*args, **kwargs):
        if inspect.iscoroutinefunction(func):
            resp = await func(*args, **kwargs)
        else:
            resp = func(*args, **kwargs)

        # 列表数据对象化
        if isinstance(resp, list):
            resp = {"lists": resp}

        resp_data = jsonable_encoder(
            resp,
            by_alias=False,
            custom_encoder={
                datetime: datetime_encoder,
                ObjectId: objectid_encoder,
            },
        )
        
        request = None
        try:
            # 变量名风格转换
            request = get_request(**kwargs)
            assert request is not None
            method_ = request.headers.get("X-Variable-Naming-Style", "snake_case")
            assert method_ != "snake_case"
            resp_data_str = vriable_naming_rename(method_, json.dumps(resp_data))
            resp_data = json.loads(resp_data_str)
        except AssertionError:
            pass

        if request is not None:
            request.state.track_info = {
                "code": HttpResp.SUCCESS.code,
                "msg": HttpResp.SUCCESS.msg,
            }
            
        return JSONResponse(
            content={
                "code": HttpResp.SUCCESS.code,
                "msg": HttpResp.SUCCESS.msg,
                "data": resp_data,
            },
            media_type="application/json;charset=utf-8",
        )

    return wrapper
