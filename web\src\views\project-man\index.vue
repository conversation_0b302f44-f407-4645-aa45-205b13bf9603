<template>
  <div class="admin">
    <el-card class="!border-none" shadow="never">
      <el-form class="mb-[-16px]" :model="formData" inline>
        <el-form-item label="项目名称">
          <el-input v-model="formData.id" class="w-[180px]" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="pager.loading" class="mt-4 !border-none" shadow="never">
      <div>
        <el-button type="primary" @click="handleShowAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新建项目
        </el-button>
      </div>
      <div class="mt-4">
        <el-table :data="pager.lists" size="large" :height="calcTableHeight()">
          <el-table-column label="项目名称" prop="prj_name" />
          <el-table-column label="状态" prop="prj_status">
            <template #default="{ row }">
              <el-tag>{{ projectStatus[row.prj_status] || "未知" }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="create_time">
            <template #default="{ row }">
              <span>{{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleProjectDetail(row.id)"> 详情 </el-button>
              <!-- <el-button link type="primary" @click="handleShowEdit(row)"> 编辑 </el-button> -->
              <el-button link type="danger" @click="handleDelete(row.id)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>

    <edit-popup
      v-if="showEdit"
      ref="editRef"
      @success="handleProjectDetail($event, 'edit')"
      @close="showEdit = false"
    />
  </div>
</template>

<script lang="ts" setup name="admin">
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
import { projectList, projectDelete } from "@/api/project-man";
import useMetadataStore from "@/stores/modules/metadata";

const metadataStore = useMetadataStore();
const projectStatus = ref([]);

const editRef = shallowRef<InstanceType<typeof EditPopup>>();

const router = useRouter();
// 表单数据
const formData = reactive({
  id: "",
});
const showEdit = ref(false);

const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: projectList,
  params: formData,
});

const calcTableHeight = () => {
  return window.innerHeight - 91 - 16 * 5 - 74 - 20 * 2 - 32 * 1;
};

const handleDelete = async (id: string) => {
  await feedback.confirm("确定要删除？");
  await projectDelete(id);
  feedback.msgSuccess("删除成功");
  getLists();
};

const handleProjectDetail = async (id: any, type: string = "detail") => {
  router.push({ path: "/detail/project", query: { id, type } });
};

const handleShowAdd = async (index: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open();
};

const handleShowEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

onMounted(async () => {
  getLists();
  projectStatus.value = await metadataStore.fetchMetadata("project_status", "kv");
});
</script>
<style lang="scss" scoped>
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
}
</style>
