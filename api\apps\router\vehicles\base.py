from typing import Annotated

from fastapi import APIRouter, Query, Request

from apps.common import unified_resp
from apps.models.common import ObjectIdStr
import apps.models.vehicle as VModel
import apps.services.vehicles as VehicleService
from apps.services.permissions import gen_dp, FuncId

router = APIRouter(prefix="/vehicles", tags=["车辆管理"])


@router.get("/", dependencies=gen_dp(FuncId.VehicleView))
@unified_resp
async def vehicle_list(req: Request, q: Annotated[VModel.Query, Query()]) -> VModel.ListOutApi:
    """获取车辆列表"""
    return await VehicleService.query(q, req.state.user)


@router.post("/", dependencies=gen_dp(FuncId.VehicleCreate))
@unified_resp
async def vehicle_create(_: Request, v: VModel.BaseInfo):
    """创建一个新的车辆"""
    return await VehicleService.create(v)


@router.delete("/{vehicle_id}", dependencies=gen_dp(FuncId.VehicleDelete))
@unified_resp
async def vehicle_delete(_: Request, vehicle_id: ObjectIdStr):
    """删除车辆"""
    return await VehicleService.delete(vehicle_id)


@router.put("/{vehicle_id}", dependencies=gen_dp(FuncId.VehicleUpdate))
@unified_resp
async def vehicles_update(req: Request, vehicle_id: ObjectIdStr, v_info: VModel.Update):
    """修改车辆信息"""
    return await VehicleService.update(vehicle_id, v_info)


@router.get("/{vehicle_id}", dependencies=gen_dp(FuncId.VehicleView))
@unified_resp
async def vehicle_info(_: Request, vehicle_id: ObjectIdStr):
    """获取车辆详细信息"""
    return await VehicleService.detail(vehicle_id)


@router.put("/{vehicle_id}/device_bind", dependencies=gen_dp(FuncId.VehicleManage))
@unified_resp
async def vehicle_bind_device(_: Request, vehicle_id: ObjectIdStr, d: VModel.Device4Vehicle):
    """绑定设备和车辆, 将未注册设备变为已注册设备"""
    return await VehicleService.bind_device(vehicle_id, d.device_id)
