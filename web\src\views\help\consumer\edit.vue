<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
      :confirmButtonText="$t('stream.确定')"
      :cancelButtonText="$t('stream.取消')"
    >
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="お客様名" prop="customer_name">
          <el-input v-model="formData.customer_name" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import { projectAdd } from "@/api/project-man";

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");

const popupTitle = computed(() => ` ${mode.value === "edit" ? "编辑" : "新規追加"} `);

const formData: any = reactive({
  customer_name: "",
});

const formRules = reactive({
  customer_name: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
});

onMounted(() => {});

const handleSubmit = async () => {
  await formRef.value?.validate();
  const { id } = await projectAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("创建项目成功");
  emit("success", id);
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (data: any) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
