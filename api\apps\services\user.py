import json
from typing import List, Optional, Dict, Any

import arrow
from bson import ObjectId
from fastapi import Request
from motor.core import AgnosticCollection
from fastapi.exceptions import RequestValidationError
from pymongo.errors import DuplicateKeyError
from starlette.exceptions import HTTPException

from apps.db import MongoDB, RedisDB
from apps.common import HttpResp, AppException
from apps.utils import singleton, sha256_str, make_token
from apps.utils.tools import Tools
import apps.models.redis_key as RedisKey
import apps.models.user as UserModel
import apps.services.role as RoleService


MENU_COLL: AgnosticCollection = MongoDB.get_collection("menus")
USER_COLL: AgnosticCollection = MongoDB.get_collection("users")
ROLE_COLL: AgnosticCollection = MongoDB.get_collection("roles")


@singleton
class MenusService:
    """菜单管理服务"""

    def __init__(self, _=None):
        pass
        # self.collection = MongoDB.get_collection("menus")

    async def get_next_id(self):
        """获取下一个菜单ID"""
        if not await RedisDB.exist(RedisKey.Menu.max_menu_id):
            cursor = MENU_COLL.find().sort("_id", -1).limit(1)
            data = await cursor.to_list(None)
            max_id = int(data[0]["_id"])
            await RedisDB.setnx(RedisKey.Menu.max_menu_id, max_id)
        return await RedisDB.incr(RedisKey.Menu.max_menu_id)

    async def menu_create(self, menu: UserModel.MenuCreate) -> dict:
        menu_json = menu.model_dump()
        next_id = await self.get_next_id()
        menu_json.update(
            {
                "_id": next_id,
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }
        )
        try:
            res = await MENU_COLL.insert_one(menu_json)
        except DuplicateKeyError as exc:
            raise AppException(HttpResp.MENU_NAME_REPEAT) from exc
        assert res.inserted_id, "Menu info insert failed."
        return {"name": menu.menu_name}

    async def menu_list(self):
        cursor = MENU_COLL.find({"_id": {"$ne": "menus"}}).sort("menu_sort", -1)
        menu_list = await cursor.to_list(None)
        res_data = [UserModel.MenuOut(**item).model_dump() for item in menu_list]
        return Tools.list_to_tree(res_data, "id", "pid", "children")

    async def menu_detail(self, id_):
        menu = await MENU_COLL.find_one({"_id": id_})
        if not menu:
            raise AppException(HttpResp.MENU_NOT_EXIST)
        return UserModel.MenuOut(**menu).model_dump()

    async def menu_update(self, menu: UserModel.MenuUpdate):
        data = json.loads(menu.model_dump_json())
        data["update_time"] = arrow.utcnow().datetime
        del data["id"]
        await MENU_COLL.update_one({"_id": menu.id}, {"$set": data})
        return {"name": menu.menu_name, "msg": "修改成功"}

    async def menu_delete(self, id_):
        res = await MENU_COLL.delete_one({"_id": id_})
        if res.deleted_count == 0:
            raise AppException(HttpResp.MENU_NOT_EXIST)
        return {"msg": "删除成功"}

    async def menu_route(self, req: Request):
        user_info: UserModel.CacheInfo = req.state.user
        query = {"menu_type": {"$in": ["M", "C"]}, "is_disable": {"$ne": 1}}
        if user_info.is_super_admin is False:
            menu_ids = await RoleService.get_roles_menus(user_info.role_ids)
            query["_id"] = {"$in": menu_ids}
        menu_list = await MENU_COLL.find(query).to_list(None)

        menu_list = [UserModel.MenuOut(**item).model_dump() for item in menu_list]

        menus_tree = Tools.list_to_tree(menu_list, "id", "pid", "children")
        return sorted(menus_tree, key=lambda x: x["menu_sort"], reverse=True)

    async def export_data(self):
        """导出菜单数据"""
        cursor = MENU_COLL.find()
        res_data = await cursor.to_list(None)
        return res_data

    async def import_data(self, data: List[dict]):
        """导入菜单数据"""
        if len(data) == 0:
            return {"msg": "导入数据为空"}
        await MENU_COLL.delete_many({})
        for i in data:
            await MENU_COLL.update_one({"_id": i["_id"]}, {"$set": i}, upsert=True)
        return {"msg": "导入成功"}


@singleton
class UserService:
    """用户管理服务"""

    def __init__(self, _=None):
        pass

    async def find_user_by_name(self, name: str) -> dict:
        user = await USER_COLL.find_one({"username": name})
        if not user:
            raise HTTPException(status_code=404, detail="User does not exist.")
        return user

    async def list(self, q: UserModel.Query):

        filter_d = {}
        if q.username:
            filter_d["username"] = q.username
        if q.nickname:
            filter_d["nickname"] = q.nickname
        if q.role:
            filter_d["role_ids"] = {"$in": [q.role]}
        filter_d.update({"status": {"$ne": UserModel.Status.DELETED.value}})

        total_count = await USER_COLL.count_documents(filter_d)
        skip = (q.page_no - 1) * q.page_size
        cursor = USER_COLL.find(filter_d).skip(skip).limit(q.page_size)
        res_data = []
        async for user in cursor:
            role_ids = user.get("role_ids", [])
            role_list = await RoleService.find_by_ids(role_ids)
            role_str = ",".join([role["name"] for role in role_list])
            res_data.append(
                {
                    "id": str(user["_id"]),
                    "username": user["username"],
                    "email": user["email"],
                    "nickname": user["nickname"],
                    "role": role_str,
                    "status": user.get("status", UserModel.Status.NORMAL.value),
                    "create_time": user.get("create_time"),
                    "last_login_time": user.get("last_login_time"),
                }
            )
        return {"count": total_count, "lists": res_data}

    async def create(self, user: UserModel.Create):
        user_json = user.model_dump()
        user_json.update(
            {
                "password": sha256_str(user.password),
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
                "last_login_time": arrow.utcnow().datetime,
                "status": UserModel.Status.NORMAL.value,
            }
        )
        try:
            res = await USER_COLL.insert_one(user_json)
            assert res.inserted_id, "User info insert failed."
            return {"username": user.username}
        except DuplicateKeyError as exc:
            raise RequestValidationError("用户名或邮箱重复") from exc

    async def delete(self, uid: ObjectId):
        if uid == UserModel.SUPER_ADMIN_ID:
            raise AppException(HttpResp.NO_PERMISSION)
        res = await USER_COLL.find_one_and_delete({"_id": uid})
        assert res, "User info delete failed."
        return {"msg": "success"}

    async def update(self, user: UserModel.Update):
        user_json = user.model_dump(exclude=set(["id"]), exclude_none=True)
        user_json.update({"update_time": arrow.utcnow().datetime})
        res = await USER_COLL.update_one({"_id": user.id}, {"$set": user_json})
        if res.modified_count == 0:
            raise AppException(HttpResp.USER_NOT_EXIST)
        await RedisDB.delete(RedisKey.UserInfo(user.id).info)
        return {"username": user.username}

    async def update_status(self, uid: ObjectId, status: UserModel.Status):
        """修改用户状态"""
        res = await USER_COLL.update_one({"_id": uid}, {"$set": {"status": status}})
        if res.modified_count == 0:
            raise HTTPException(status_code=404, detail="User does not exist.")
        await RedisDB.delete(RedisKey.UserInfo(uid).info)
        return {"msg": "修改成功"}

    async def update_password(self, uid: ObjectId, new_password: str, old_password: Optional[str] = None):
        """修改密码, 有旧密码则校验旧密码, 否则不需要
        @param uid: 用户ID
        @param new_password: 新密码
        @param old_password: 旧密码
        @return:
        """
        query: Dict[Any, Any] = {}
        query["_id"] = uid

        if old_password is not None:
            query["password"] = sha256_str(old_password)

        user = await USER_COLL.find_one(query, {"username": 1})
        if user is None:
            raise AppException(HttpResp.LOGIN_ACCOUNT_ERROR)

        res = await USER_COLL.update_one(query, {"$set": {"password": sha256_str(new_password)}})
        if res.modified_count == 0:
            raise AppException(HttpResp.LOGIN_ACCOUNT_ERROR)

        # delete user info cache
        await RedisDB.delete(RedisKey.UserInfo(uid).info)

        return {"username": user["username"], "msg": "success"}

    async def detail(self, uid: ObjectId):
        """查询用户详情"""
        user = await USER_COLL.find_one({"_id": uid})
        if not user:
            raise AppException(HttpResp.USER_NOT_EXIST)
        user_info = UserModel.CacheInfo(**user)
        res_data = user_info.model_dump(exclude={"is_super_admin"})
        return res_data

    async def info(self, req: Request):
        """获取用户信息, 包含权限信息"""
        user: UserModel.CacheInfo = req.state.user
        assert user, "User info is required."
        data = user.model_dump(by_alias=True)
        result = UserModel.FullInfo(
            user=UserModel.BaseInfo(**data),
            permissions=[],
        )

        if user.is_super_admin:
            result.permissions = ["*"]
            return result

        role_results = await ROLE_COLL.aggregate(
            [
                {"$match": {"_id": {"$in": user.role_ids}}},
                {"$unwind": "$menus"},
                {"$group": {"_id": None, "all_menus": {"$addToSet": "$menus"}}},
            ]
        ).to_list(length=None)
        menu_id_list = role_results[0].get("all_menus", [])

        menu_results = MENU_COLL.find(
            {
                "_id": {"$in": menu_id_list},
                "menu_type": {"$in": ["A", "C"]},
            }
        )
        async for menu in menu_results:
            if menu["perms"] == "":
                continue
            result.permissions.append(menu["perms"])
        return result

    async def login(self, data: UserModel.Login, req: Optional[Request] = None):
        q = {"username": data.username, "password": sha256_str(data.password)}
        user_data = await USER_COLL.find_one(q)
        if user_data is None:
            raise AppException(HttpResp.LOGIN_ACCOUNT_ERROR)
        if user_data.get("status") != UserModel.Status.NORMAL.value:
            raise AppException(HttpResp.LOGIN_DISABLE_ERROR)

        await USER_COLL.update_one(
            {"_id": user_data["_id"]},
            {"$set": {"last_login_time": arrow.utcnow().datetime}},
        )

        uikey = RedisKey.UserInfo(user_data["_id"])
        utkey = RedisKey.UserToken(make_token())
        user_data["role_ids"] = [str(role_id) for role_id in user_data.get("role_ids", [])]
        user_info = UserModel.BaseInfo(**user_data)
        await RedisDB.set(utkey.token, uikey.id, 7200)
        await RedisDB.set(uikey.info, user_info.model_dump_json())

        # 注入用户信息，用于后续接口调用
        if req:
            req.state.user = user_info

        return {"token": utkey.id}

    async def logout(self, req: Request):
        """退出登录，清除 token 和 user_info 缓存"""
        assert req.state.user_token, "Token is required."
        user_token_k = RedisKey.UserToken(req.state.user_token).token
        user_info_k = RedisKey.UserInfo(req.state.user.id).info
        await RedisDB.delete(user_token_k)
        await RedisDB.delete(user_info_k)
        return {"msg": "success"}
