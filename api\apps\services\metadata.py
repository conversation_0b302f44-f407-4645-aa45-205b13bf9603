from typing import List

from pymongo.errors import DuplicateKeyError

from apps.db import MongoDB
from apps.common import AppException, HttpResp
import apps.models.metadata as MetaModel


COLL = MongoDB.get_collection("meta_data")


async def list_all() -> List[MetaModel.BaseMeta]:
    results = COLL.find({})
    data = []
    async for result in results:
        data.append(MetaModel.BaseMeta(**result))
    return data


async def get_metadata(key: str) -> MetaModel.BaseMeta:
    data = await COLL.find_one({"key": key})
    if data is None:
        raise AppException(HttpResp.METADATA_NOT_FOUND)
    return MetaModel.BaseMeta(**data)


async def add_metadata(data: MetaModel.BaseMeta):
    try:
        await COLL.insert_one(data.model_dump())
    except DuplicateKeyError:
        raise AppException(HttpResp.METADATA_EXISTS)
    return {}


async def update_metadata(key: str, data: MetaModel.BaseMeta):
    result = await COLL.update_one(
        {"key": key},
        {"$set": data.model_dump(exclude={"key"})},
    )
    assert result.modified_count == 1, "update metadata failed."
    return {}


async def delete_metadata(key_name: str):
    result = await COLL.delete_one({"key": key_name})
    assert result.deleted_count == 1, "delete metadata failed."
    return {}
