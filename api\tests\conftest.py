import os
import secrets

import pytest
from pymongo import MongoClient
from fastapi.testclient import TestClient


# 覆盖默认配置，切到测试环境
os.environ["OMS_MODE"] = "test"
TEST_PORT = 64561


@pytest.fixture(scope="session")
def app_config():
    from apps.config import get_settings

    yield get_settings()


@pytest.fixture(scope="session")
def rdb(app_config):
    from redis import Redis

    uri = f"redis://{app_config.redis_host}:{app_config.redis_port}/{app_config.redis_db}"
    rdb_ = Redis.from_url(uri)
    assert rdb_.ping() is True
    rdb_.flushdb()
    yield rdb_
    rdb_.close()


@pytest.fixture(scope="session")
def mdb(app_config):

    uri = "mongodb://{}:{}@{}:{}/{}?authSource=admin".format(
        app_config.mongo_username,
        app_config.mongo_password,
        app_config.mongo_host,
        app_config.mongo_port,
        app_config.mongo_db_name,
    )
    mdb_ = MongoClient(uri, timeoutMS=2000)
    assert mdb_.server_info() is not None
    mdb_.drop_database(app_config.mongo_db_name)
    test_db = mdb_[app_config.mongo_db_name]
    yield test_db
    mdb_.close()


@pytest.fixture(scope="session")
def app_run(rdb, mdb):
    """启动 uvicorn 服务"""
    from apps import create_app

    yield create_app()


@pytest.fixture(scope="session")
def admin_token(rdb, mdb, app_run):
    """初始化管理员 token"""
    from apps.models.user import BaseInfo

    user_coll = mdb["users"]
    user = user_coll.find_one({"username": "admin"})
    assert user is not None
    user_info = BaseInfo(**user)
    user_id = str(user_info.id)

    token = f"test_{secrets.token_hex(16)[5:]}"
    user_token_key = f"oms:user:token:{token}"
    user_info_key = f"oms:user:info:{user_id}"
    rdb.set(user_token_key, user_id)
    rdb.set(user_info_key, user_info.model_dump_json())
    assert rdb.get(user_token_key) is not None
    yield token
    rdb.delete(user_token_key)


@pytest.fixture(scope="session")
def admin_headers(admin_token):
    """管理员请求头"""
    headers = {"authorization": f"Bearer {admin_token}"}
    yield headers


@pytest.fixture(scope="session")
def client(app_run):
    """普通客户端"""
    with TestClient(app_run) as ci:
        res = ci.post("/internal/init_data")
        assert res.status_code == 200
        ci.base_url = "http://test/api/v1"
        yield ci
