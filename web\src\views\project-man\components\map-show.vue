<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";

const props = defineProps({
  lng: {
    type: Number,
    required: true,
  },
  lat: {
    type: Number,
    required: true,
  },
});

const { lng, lat } = toRefs(props);

let map: any = null;

const initMap = () => {
  AMapLoader.load({
    key: "f0f7e58b7882db63bffab5fa22f8611c", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: [
      "AMap.moveAnimation", // 动画插件
      "AMap.Scale", // 右下角缩略图插件 比例尺
      "AMap.MapType", // 地图类型切换插件，可用于切换卫星地图
      "AMap.Geolocation", // 定位控件，用来获取和展示用户主机所在的经纬度位置
      "AMap.Geocoder", // 逆地理编码,通过经纬度获取地址所在位置详细信息
    ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  })
    .then((AMap) => {
      let position = new AMap.LngLat(lng.value, lat.value);

      map = new AMap.Map("container", {
        viewMode: "3D", // 是否为3D地图模式
        zoom: 16, // 初始化地图级别
        center: position,
        resizeEnable: true,
      });

      let markerContent =
        "" +
        '<div class="custom-content-marker">' +
        '   <img style="width: 30px;" src="//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png">' +
        "</div>" +
        "";

      let marker = new AMap.Marker({
        position: position,
        // 将 html 传给 content
        content: markerContent,
        // 以 icon 的 [center bottom] 为原点
        offset: new AMap.Pixel(-13, -30),
      });

      map.add(marker);
    })
    .catch((e) => {
      console.log(e);
    });
};

const reload = async () => {
  await map?.destroy();
  await initMap();
};

onMounted(() => {
  //@ts-ignore
  window._AMapSecurityConfig = {
    securityJsCode: "163aa7cfca1e67b5d6eb627b2e014b57", // "安全密钥",
  };
  initMap();
});

onUnmounted(() => {
  map?.destroy();
});

defineExpose({
  reload,
});
</script>

<template>
  <div id="container"></div>
</template>

<style scoped>
#container {
  width: 100%;
  height: 100%;
}
</style>
