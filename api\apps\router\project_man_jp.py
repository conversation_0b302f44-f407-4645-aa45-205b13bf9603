"""
    作业指导管理增删改查
"""

from typing import Annotated
from fastapi import APIRouter, Request, Query
from apps.common import unified_resp
import apps.services.project_man_jp as ProjectService
import apps.models.project_man_jp as ProjectModel


router = APIRouter(prefix="/project", tags=["项目管理"])


@router.post("/", tags=["项目管理"])
@unified_resp
async def project_create(_: Request, project: ProjectModel.ProjectCreate):
    return await ProjectService.project_create(_, project)


@router.get("/weather", tags=["项目管理"])
@unified_resp
async def project_weather(_: Request):
    return await ProjectService.get_weather(_)


@router.get("/workbench", tags=["项目管理"])
@unified_resp
async def project_workbench_list(req: Request):
    return await ProjectService.project_workbench_list(req)


@router.get("/", tags=["项目管理"])
@unified_resp
async def project_list(_: Request, q: Annotated[ProjectModel.ProjectQuery, Query()]):
    return await ProjectService.project_list(_, q)


@router.put("/{project_id}/base", tags=["项目管理"])
@unified_resp
async def project_base_update(_: Request, project_id: str, base: ProjectModel.ProjectBaseUpdate):
    return await ProjectService.project_base_update(project_id, base)


@router.post("/{project_id}/vehicle", tags=["项目管理"])
@unified_resp
async def project_vehicle_add(_: Request, project_id: str, vehicle: ProjectModel.ProjectVehicleAdd):
    return await ProjectService.project_vehicle_add(project_id, vehicle)


@router.put("/{project_id}/vehicle/{vehicle_id}", tags=["项目管理"])
@unified_resp
async def project_vehicle_update(_: Request, project_id: str, vehicle_id: str, vehicle: ProjectModel.ProjectVehicleAdd):
    return await ProjectService.project_vehicle_update(project_id, vehicle_id, vehicle)


@router.delete("/{project_id}/vehicle/{vehicle_id}", tags=["项目管理"])
@unified_resp
async def project_vehicle_delete(_: Request, project_id: str, vehicle_id: str):
    return await ProjectService.project_vehicle_delete(project_id, vehicle_id)


@router.post("/{project_id}/console", tags=["项目管理"])
@unified_resp
async def project_console_add(_: Request, project_id: str, console: ProjectModel.ProjectConsoleAdd):
    return await ProjectService.project_console_add(project_id, console)


@router.put("/{project_id}/console/{console_id}", tags=["项目管理"])
@unified_resp
async def project_console_update(_: Request, project_id: str, console_id: str, console: ProjectModel.ProjectConsoleAdd):
    return await ProjectService.project_console_update(project_id, console_id, console)


@router.delete("/{project_id}/console/{console_id}", tags=["项目管理"])
@unified_resp
async def project_console_delete(_: Request, project_id: str, console_id: str):
    return await ProjectService.project_console_delete(project_id, console_id)


@router.put("/{project_id}", tags=["项目管理"])
@unified_resp
async def project_update(_: Request, project_id: str, project: ProjectModel.ProjectCreate):
    return await ProjectService.project_update(project_id, project)


@router.delete("/{project_id}", tags=["项目管理"])
@unified_resp
async def project_delete(_: Request, project_id: str):
    return await ProjectService.project_delete(project_id)


@router.get("/{project_id}", tags=["项目管理"])
@unified_resp
async def project_detail(_: Request, project_id: str):
    return await ProjectService.project_detail(project_id)
