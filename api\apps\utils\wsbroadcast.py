from typing import Union

from bson import ObjectId
from broadcaster import Broadcast

from apps.config import get_settings
import apps.models.websocket as WSModel


_c = get_settings()

WBroadCast = Broadcast(f"redis://:{_c.redis_password}@{_c.redis_host}:{_c.redis_port}/{_c.redis_db}")


async def send_msg_to_client(
    id_: Union[str, ObjectId],
    data: Union[WSModel.VehicleMsg, WSModel.OPCMsg, WSModel.UserMsg],
):
    """发送消息, type_ 为设备类型, id_ 为设备 ID"""
    if isinstance(id_, ObjectId):
        id_ = str(id_)
    else:
        id_ = id_.strip()
    data.user = "server"
    await WBroadCast.publish(id_, data.model_dump_json())
