<template>
  <div class="vehicle-card">
    <div class="content ml-2 pt-1">
      <div class="base-item flex flex-row">
        <div class="label">项目名称:</div>
        <div class="value">{{ prj_name || "--" }}</div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">项目状态:</div>
        <div class="value">{{ statusEnum[prj_status] || "--" }}</div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">项目位置:</div>
        <div class="value">
          {{ prj_address || "--" }}
        </div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">设备描述:</div>
        <div class="value">{{ devices_info || "--" }}</div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">甲方单位:</div>
        <div class="value">{{ first_party || "--" }}</div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">销售经理:</div>
        <div class="value">{{ salesperson || "--" }}</div>
      </div>
      <div class="base-item flex flex-row">
        <div class="label">现场工程师:</div>
        <div class="value">{{ installer_info || "--" }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import useMetadataStore from "@/stores/modules/metadata";
const metadataStore = useMetadataStore();

const statusEnum = ref([]);

const props = defineProps({
  prj_name: {
    type: String,
    default: "",
  },
  prj_status: {
    type: Number,
    default: 1,
  },
  prj_address: {
    type: String,
    default: "",
  },
  devices_info: {
    type: String,
    default: "",
  },
  first_party: {
    type: String,
    default: "",
  },
  salesperson: {
    type: String,
    default: "",
  },
  installer_info: {
    type: String,
    default: "",
  },
});

onMounted(async () => {
  statusEnum.value = await metadataStore.fetchMetadata("project_status", "kv");
});
</script>
<style scoped lang="scss">
.base-item {
  min-height: 30px;
  line-height: 30px;
  display: flex;
  align-items: center;
  .label {
    min-width: 100px;
    color: #222831;
  }
  .value {
    color: #393e46;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
