from typing import Annotated
from fastapi import APIRouter, Form, Query, Request, UploadFile
from fastapi.responses import StreamingResponse, FileResponse

import apps.models.material as MaterialModel
import apps.services.material as MaterialService
from apps.models.common import ObjectIdStr
from apps.common import unified_resp, HttpResp, AppException

router = APIRouter(prefix="/material", tags=["素材管理"])


@router.get("/cate_list", tags=["素材管理"])
@unified_resp
async def get_cate_list(_: Request, type: int = 1):
    return await MaterialService.get_cate_list(type)


@router.post("/cate_add", tags=["素材管理"])
@unified_resp
async def add_cate(_: Request, data: MaterialModel.MaterialCate):
    return await MaterialService.add_cate(data)


@router.delete("/cate_delete/{cate_id}", tags=["素材管理"])
@unified_resp
async def delete_cate(_: Request, cate_id: int):
    return await MaterialService.delete_cate(cate_id)


@router.put("/cate_update/{cate_id}", tags=["素材管理"])
@unified_resp
async def update_cate(_: Request, cate_id: int, data: MaterialModel.MaterialCateEdit):
    return await MaterialService.update_cate(cate_id, data)


@router.post("/upload", tags=["素材管理"])
@unified_resp
async def upload_material(file: UploadFile, cid: int = Form(...)):
    return await MaterialService.upload_material(file, cid)


@router.get("/media_list", tags=["素材管理"])
@unified_resp
async def media_list(_: Request, q: Annotated[MaterialModel.MaterialQuery, Query()]):
    return await MaterialService.get_material_list(q)


@router.get("/content/{material_id}", tags=["素材管理"])
async def material_content_get(
    _: Request,
    material_id: ObjectIdStr,
    type: str = "file",
    download: int = 0
):
    """获取素材内容

    Args:
        material_id: 素材ID
        type: 请求类型，"cover"表示获取缩略图，"file"表示获取原文件
        download: 是否强制下载，1表示下载，0表示预览
    """
    file_path, file_type, media_type = await MaterialService.get_material_data_by_id(material_id, type)
    print(f'material file_path: {file_path}, file_type: {file_type}, media_type: {media_type}')

    # 如果是下载请求，设置强制下载的响应头
    if download == 1:
        import os
        filename = os.path.basename(file_path)
        headers = {
            "Content-Disposition": f"attachment; filename*=UTF-8''{filename}",
            "Content-Type": "application/octet-stream"
        }
        return FileResponse(file_path, headers=headers, filename=filename)
    else:
        # 正常预览模式
        return FileResponse(file_path, media_type=media_type)


@router.delete("/delete", tags=["素材管理"])
@unified_resp
async def material_delete(_: Request, material_ids: list[ObjectIdStr]):
    return await MaterialService.delete_material(material_ids)
