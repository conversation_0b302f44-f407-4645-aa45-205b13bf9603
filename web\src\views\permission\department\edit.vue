<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
    >
      <el-form class="ls-form" ref="formRef" :rules="rules" :model="formData" label-width="80px">
        <el-form-item label="部门名称" prop="name">
          <el-input
            class="ls-input"
            v-model="formData.name"
            placeholder="请输入部门名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="上级部门" prop="parent_id">
          <el-tree-select
            v-model="formData.parent_id"
            :data="departmentOptions"
            placeholder="请选择上级部门"
            clearable
            check-strictly
            :render-after-expand="false"
            node-key="id"
            :props="{ label: 'name', children: 'children' }"
          />
        </el-form-item>
        <el-form-item label="显示排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="部门状态" prop="is_enable">
          <el-radio-group v-model="formData.is_enable">
            <el-radio :label="0">正常</el-radio>
            <el-radio :label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            placeholder="请输入备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import {
  departmentAdd,
  departmentDetail,
  departmentEdit,
  departmentTree,
} from "@/api/perms/department";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑部门" : "新增部门";
});

const formData = reactive({
  id: "",
  name: "",
  parent_id: "",
  sort: 100,
  is_enable: 0,
  remark: "",
});

const departmentOptions = ref<any[]>([]);

const rules = {
  name: [
    {
      required: true,
      message: "请输入部门名称",
      trigger: ["blur"],
    },
  ],
};

// 获取部门树形数据
const getDepartmentTree = async () => {
  try {
    const { lists: res } = await departmentTree();
    // 添加一个顶级节点
    departmentOptions.value = [{ id: "0", name: "顶级部门", children: res }];
  } catch (error) {
    console.error("获取部门树形数据失败", error);
  }
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  const params = { ...formData };
  // 如果parent_id为空字符串，则设置为null
  if (params.parent_id === "0") {
    // @ts-ignore
    params.parent_id = null;
  }
  mode.value == "edit" ? await departmentEdit(params) : await departmentAdd(params);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const handleClose = () => {
  emit("close");
};

const open = async (type = "add") => {
  mode.value = type;
  await getDepartmentTree();
  popupRef.value?.open();
};

const setFormData = async (row: Record<any, any>) => {
  const data = await departmentDetail(row.id);
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      //@ts-ignore
      formData[key] = data[key];
    }
  }
};

// 设置父部门ID
const setParentId = (parentId: string) => {
  formData.parent_id = parentId;
};

defineExpose({
  open,
  setFormData,
  setParentId,
});
</script>
