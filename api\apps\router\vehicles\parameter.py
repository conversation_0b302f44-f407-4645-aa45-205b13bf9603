from typing import Union, List

from fastapi import APIRouter, Request

from apps.common import unified_resp
from apps.models.common import ObjectIdStr
import apps.models.ros as RosModel
import apps.models.vehicle as VModel
import apps.services.vehicles as VehicleService
from apps.services.permissions import gen_dp
from apps.services.permissions import RoleResourceFlag as R, FuncId as F


router = APIRouter(prefix="/vehicles/{vehicle_id}/parameter", tags=["车辆参数"])


@router.get("/metadata", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def meta_params_get(req: Request, vehicle_id: ObjectIdStr):
    """元数据查询"""
    meta = VehicleService.Meta(vehicle_id)
    return await meta.get_all()


@router.get("/metadata/{key_name}", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def meta_params_query(
    req: Request,
    vehicle_id: ObjectIdStr,
    key_name: str,
):
    """元数据查询"""
    meta = VehicleService.Meta(vehicle_id)
    return await meta.query(key_name)


@router.post("/metadata", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def meta_params_set(
    _: Request,
    vehicle_id: ObjectIdStr,
    data: VModel.MetaData,
):
    """元数据更新"""
    meta = VehicleService.Meta(vehicle_id)
    return await meta.add(data)


@router.put("/metadata", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def meta_params_update(
    _: Request,
    vehicle_id: ObjectIdStr,
    data: VModel.MetaData,
):
    """元数据更新"""
    meta = VehicleService.Meta(vehicle_id)
    return await meta.update(data)


@router.delete("/metadata", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def meta_params_delete(
    _: Request,
    vehicle_id: ObjectIdStr,
    key_name: str,
):
    """元数据更新"""
    meta = VehicleService.Meta(vehicle_id)
    return await meta.delete(key_name)


@router.post("/program_node", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def program_node_params_set(
    _: Request,
    vehicle_id: ObjectIdStr,
    node: RosModel.Node,
):
    """增加车辆设备上的ros程序节点"""
    srv = VehicleService.ROS(vehicle_id)
    return await srv.add_node(node)


@router.get("/program_node", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def program_node_params_query(_: Request, vehicle_id: ObjectIdStr):
    """获取车辆上的参数"""
    srv = VehicleService.ROS(vehicle_id)
    return await srv.get_all_node()


@router.post("/program_node/control", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def program_node_control(
    _: Request,
    vehicle_id: ObjectIdStr,
    node_ctrl: RosModel.NodeCrontrol,
):
    """获取车辆上的参数"""
    srv = VehicleService.ROS(vehicle_id)
    return await srv.control_node(node_ctrl)


@router.post("/program_node/import_nodes", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def program_node_import(
    _: Request,
    vehicle_id: ObjectIdStr,
    nodes: List[RosModel.Node],
):
    """修改车辆设备上的ros程序节点"""
    srv = VehicleService.ROS(vehicle_id)
    return await srv.import_nodes(nodes)


@router.put("/program_node", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def program_node_params_update(
    _: Request,
    vehicle_id: ObjectIdStr,
    node: RosModel.UpdateParams,
):
    """修改车辆设备上的ros程序节点"""
    srv = VehicleService.ROS(vehicle_id)
    return await srv.update_node_params(node)


@router.delete("/program_node", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def program_node_delete(
    _: Request,
    vehicle_id: ObjectIdStr,
    node_name: str,
):
    """删除车辆设备上的ros程序节点"""
    srv = VehicleService.ROS(vehicle_id)
    return await srv.del_node(node_name)


@router.get("/can", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def vehicle_can_list(_: Request, vehicle_id: ObjectIdStr):
    """车辆设备CAN列表"""
    srv = VehicleService.CAN(vehicle_id)
    return await srv.get_can()


@router.put("/can", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def vehicle_can_update(
    _: Request,
    vehicle_id: ObjectIdStr,
    can_data: List[VModel.CAN],
):
    """更新车辆上的CAN配置"""
    srv = VehicleService.CAN(vehicle_id)
    return await srv.update_can(can_data)


@router.get("/cameras", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def vehicle_camera_list(
    _: Request,
    vehicle_id: ObjectIdStr,
    camera_type: int,
):
    """车辆设备相机列表"""
    srv = VehicleService.Camera(vehicle_id)
    return await srv.get_all_cam(VModel.CameraType(camera_type))


@router.put("/cameras", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def vehicle_camera_update(
    _: Request,
    vehicle_id: ObjectIdStr,
    camera_type: int,
    camera_data: List[Union[VModel.GMSLCamera, VModel.IPCamera]],
):
    """更新车辆上的相机配置"""
    srv = VehicleService.Camera(vehicle_id)
    return await srv.update_cam(VModel.CameraType(camera_type), camera_data)


@router.get("/cameras/status", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def vehicl_camera_status_list(_: Request, vehicle_id: ObjectIdStr):
    """车辆设备相机状态列表"""
    srv = VehicleService.Camera(vehicle_id)
    return await srv.get_all_cam_status()


@router.put("/cameras/status", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def vehicle_camera_status_update(
    _: Request,
    vehicle_id: ObjectIdStr,
    camera_data: List[VModel.CameraStatus],
):
    """更新车辆上的相机状态配置"""
    srv = VehicleService.Camera(vehicle_id)
    return await srv.update_cam_status(camera_data)


@router.get("/cameras/enable", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def vehicle_enable_cameras(_: Request, vehicle_id: ObjectIdStr):
    """获取已经开启的相机列表"""
    srv = VehicleService.Camera(vehicle_id)
    return await srv.get_enable_cam()


@router.put("/data_sync", dependencies=gen_dp(F.VehicleUpdate, R.ADMIN))
@unified_resp
async def device_params_data_sync(_: Request, vehicle_id: ObjectIdStr):
    """安卓数据查询"""
    return await VehicleService.sync_device_params(vehicle_id)


@router.get("/android", dependencies=gen_dp(F.VehicleUpdate, R.OPERATOR))
@unified_resp
async def android_params_query(_: Request, vehicle_id: ObjectIdStr):
    """安卓数据查询"""
    srv = VehicleService.Android(vehicle_id)
    return await srv.get_params()


@router.put("/android", dependencies=gen_dp(F.VehicleUpdate, R.OPERATOR))
@unified_resp
async def android_params_update(
    _: Request,
    vehicle_id: ObjectIdStr,
    data: VModel.AndroidParams,
):
    """安卓数据更新"""
    srv = VehicleService.Android(vehicle_id)
    return await srv.update_params(data)
