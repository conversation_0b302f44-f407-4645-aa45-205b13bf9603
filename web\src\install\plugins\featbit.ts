export {  };

// import fbClient from "featbit-js-client-sdk";
// import { defineStore } from "pinia";
// import feedback from "./utils/feedback";

// export const featBit = {
//   install(app: any, options: any) {
//     fbClient.init({
//       secret: "pz_1eD4jV0SEilzFrhjDXwxOAQG08NOEqgK9xjrkmGdA",
//       api: "https://streaming.featureflag.co",
//       user: {
//         name: "tester",
//         keyId: "tester-id",
//         customizedProperties: [
//           {
//             name: "group",
//             value: "qa",
//           },
//         ],
//       },
//     });

//     const store = useFeatBitStore();

//     fbClient.on("ff_update:test-cc", (changes: any) => {
//       feedback.msgSuccess(`开关状态test_cc已切换至 ${changes.newValue ? "开" : "关"}`);
//     });
//     fbClient.on("ff_update:tesst-cc-json", (changes: any) => {
//       console.log(changes);
//       feedback.msgSuccess(`开关状态tesst-cc-json已切换至 ${changes.newValue}`);
//     });

//     fbClient.waitUntilReady().then((changes) => console.log("ff_update:test-cc", changes));
//   },
// };

// export const useFeatBitStore = defineStore("featbit", {
//   state: () => ({
//     flags: false,
//   }),
// });
