import type { LocaleSetting, LocaleType } from "@/locales/lang";

import { defineStore } from "pinia";

interface LocaleState {
  localInfo: LocaleSetting;
}

export const useLocaleStore = defineStore({
  id: "locale",
  state: (): LocaleState => ({
    localInfo: {
      locale: "zh_CN",
      fallback: "zh_CN",
      availableLocales: ["zh_CN", "en", "ja"],
    },
  }),
  getters: {
    getLocale(state): LocaleType {
      return state.localInfo?.locale ?? "zh_CN";
    },
  },
  actions: {
    setLocaleInfo(info: Partial<LocaleSetting>) {
      this.localInfo = { ...this.localInfo, ...info };
    },

    initLocale() {
      this.setLocaleInfo(this.localInfo);
    },
  },
  persist: [
    {
      pick: ["localInfo"],
    },
  ],
});

export default useLocaleStore;
