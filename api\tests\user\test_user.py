import json
import pytest

from apps.utils import sha256_str


@pytest.mark.dependency()
def test_user_login(client):
    """测试用户登录"""
    data = {"username": "admin", "password": "builderx"}
    res = client.post("/user/login", content=json.dumps(data))
    res_data = res.json()
    assert res_data["code"] == 200
    token = res_data["data"]["token"]
    assert len(token) == 32


def test_user_info(client, admin_headers):
    """测试获取用户信息"""
    res = client.get("/user/info", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    user_info = res_data["data"]["user"]
    assert user_info["username"] == "admin"


@pytest.mark.dependency()
def test_create_user(client, mdb, admin_headers):
    """测试创建用户"""
    data = {
        "username": "pytest_user_123",
        "nickname": "测试用户",
        "password": "12345678",
        "email": "<EMAIL>",
        "avatar": "",
        "role_ids": [],
    }
    res = client.post("/user/", json=data, headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user is not None


def test_status_user(client, mdb, admin_headers):
    """测修改用户状态"""
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user is not None
    uid = str(user["_id"])
    data = {"id": uid, "status": 1}
    res = client.put("/user/status", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user["status"] == 1


def test_update_user(client, mdb, admin_headers):
    """测试编辑用户"""
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user is not None
    uid = str(user["_id"])
    data = {
        "id": uid,
        "role_ids": ["bbbbbbbbbbbbbbbbbbbbbbbb", "aaaaaaaaaaaaaaaaaaaaaaaa"],
    }
    res = client.put("/user/", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert len(user["role_ids"]) == 2


def test_update_password(client, mdb, admin_headers):
    """测试修改用户密码"""
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user is not None
    uid = str(user["_id"])
    data = {"id": uid, "old_password": "12345678", "new_password": "123456789"}
    res = client.put("/user/me/password", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user["password"] == sha256_str("123456789")


@pytest.mark.dependency(depends=["test_create_user"])
def test_delete_user(client, mdb, admin_headers):
    """测试删除用户"""
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user is not None
    uid = str(user["_id"])
    res = client.delete("/user/", headers=admin_headers, params={"uid": uid})
    assert res.json()["code"] == 200
    user = mdb["users"].find_one({"username": "pytest_user_123"})
    assert user is None


def test_query_user(client, mdb, admin_headers):
    """测试查询用户"""
    res = client.get("/user/", headers=admin_headers, params={"page_no": 1, "page_size": 10})
    res_data = res.json()
    assert res_data["code"] == 200
    assert len(res_data["data"]["lists"]) > 0


def test_user_logout(rdb, client, admin_headers):
    """测试用户登出"""
    res = client.get("/user/logout", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    user_token_key = f"oms:user:token:{admin_headers}"
    assert rdb.get(user_token_key) is None
