import request from "@/utils/request";

// 新增会员
export function memberAdd(params: Record<string, any>) {
  return request.post({ url: "/v1/member", params });
}

// 会员列表
export function memberList(params: Record<string, any>) {
  return request.get({ url: "/v1/member", params });
}

// 会员详情
export function memberDetail(id: string) {
  return request.get({ url: `/v1/member/${id}` });
}

// 编辑会员
export function memberEdit(params: Record<string, any>) {
  return request.put({ url: `/v1/member/${params.id}`, params });
}

// 删除会员
export function memberDelete(id: string) {
  return request.delete({ url: `/v1/member/${id}` });
}

// 上传会员照片
export function uploadMemberPhoto(file: File, memberId: string) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("member_id", memberId);
  
  return request.post({
    url: "/v1/member/upload_photo",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
