from typing import Optional, Text
from pydantic import BaseModel

from pydantic import BaseModel, Field
from datetime import datetime
from .common import PagingModel, ObjectIdStr


class GuideCreate(BaseModel):
    name: str
    desc: Optional[str] = None
    content: Optional[list] = None


class GuideQuery(PagingModel):
    name: Optional[str] = None


class GuideOut(GuideCreate):
    id: ObjectIdStr = Field(..., alias="_id")
    create_time: Optional[datetime] = None


class GuideItemCreate(BaseModel):
    title: str
    content: Text

class GuideItemUpdate(GuideItemCreate):
    id: int