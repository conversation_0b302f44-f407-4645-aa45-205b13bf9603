from typing import Optional, Text
from pydantic import BaseModel

from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from .common import PagingModel, ObjectIdStr
from pydantic import BaseModel

BaseModel.model_config = ConfigDict(
    json_encoders={
        datetime: lambda dt: dt.strftime("%Y-%m-%dT%H:%M:%SZ")
    }
)

class ProjectCreate(BaseModel):
    customer_name: str = Field(..., description="客户名称")

class ProjectQuery(PagingModel):
    customer_name: Optional[str] = None
    sort_field: Optional[str] = None
    sort_order: Optional[str] = None


class ProjectOut(ProjectCreate):
    id: ObjectIdStr = Field(..., alias="_id")
    create_time: datetime


class ProjectListOut(ProjectCreate):
    id: ObjectIdStr = Field(..., alias="_id")
    customer_name: Optional[str] = None  # 客户名称
    person_name: Optional[str] = None  # 项目地址
    contact_info: Optional[str] = None  # 联系方式
    vehicle: list[dict] # 车辆信息
    console: list[dict] # 控制台信息
    create_time: datetime
    update_time: datetime


class ProjectDetailOut(ProjectOut):
    prj_address: Optional[str] = None  # 项目地址
    customer_name: Optional[str] = None  # 客户名称
    contact_info: Optional[str] = None  # 联系方式
    person_name: Optional[str] = None  # 负责人名称
    devices_info: Optional[str] = None
    vehicle: list[dict] # 车辆信息
    console: list[dict] # 控制台信息
    create_time: datetime  # 创建时间
    update_time: datetime


class ProjectVehicleOut(BaseModel):
    id: ObjectIdStr = Field(..., alias="_id")
    name: str


class ProjectBaseUpdate(BaseModel):
    customer_name: Optional[str] = None  # 客户名称
    contact_info: Optional[str] = None  # 联系方式
    person_name: Optional[str] = None  # 负责人名称


class ProjectVehicleAdd(BaseModel):
    vehicle_id: Optional[str] = None
    vehicle_name: str = Field(..., description="车辆名称")
    vehicle_type: int = Field(..., description="车辆类型")
    vehicle_sn: Optional[str] = Field(None, description="S/N码")
    vehicle_address: Optional[str] = Field(None, description="车辆位置")
    smart_feature: Optional[list] = Field([], description="智能功能")
    install_date: Optional[datetime] = Field(None, description="安装日期")
    delivery_date: Optional[datetime] = Field(None, description="交付日期")
    warranty_end_date: Optional[datetime] = Field(None, description="质保期至")


class ProjectConsoleAdd(BaseModel):
    console_name: str = Field(..., description="操作台名称")
    console_type: int = Field(..., description="操作台类型")
    console_sn: Optional[str] = Field(None, description="S/N码")
    console_address: Optional[str] = Field(None, description="操作台位置")
    install_date: Optional[datetime] = Field(None, description="安装日期")
    delivery_date: Optional[datetime] = Field(None, description="交付日期")
    warranty_end_date: Optional[datetime] = Field(None, description="质保期至")
