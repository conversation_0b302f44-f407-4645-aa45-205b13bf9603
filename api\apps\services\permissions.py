import json
from typing import Optional, Union, List, Any, Tuple
from functools import lru_cache

from bson import ObjectId
from fastapi import Request, WebSocket, Depends
from fastapi.security import OAuth2PasswordBearer

from apps.db import RedisDB
from apps.config import get_settings
from apps.common import AppException, HttpResp
import apps.models.redis_key as RedisKey
import apps.models.user as UserModel
import apps.services.role as RoleService
from apps.services.user import UserService
from apps.models.permissions import FuncId, RoleResourceFlag


TOKEN_EXPIRE = 7200


@lru_cache(maxsize=128)
def is_not_auth_url(url) -> bool:
    """是否是不需要登录的 uri"""
    for no_auth_url in get_settings().not_auth_uri:
        if no_auth_url.match(url):
            return True
    return False


class Access(OAuth2PasswordBearer):
    """
    自定义的 权限校验
    继承 OAuth2PasswordBearer 类，是为了让 FastAPI 自动生成的文档中的登录按钮生效
    """

    def __init__(self):
        super().__init__(tokenUrl="/api/v1/user/auth")
        self.auth_token = ""
        self.auth_type = ""

    async def check_user_info(self, req: Union[Request, WebSocket]):
        """Redis 中检查 token 是否有效, 并缓存用户信息加入到 request.state"""
        assert self.auth_type == "Bearer"
        user_token_key = RedisKey.UserToken(self.auth_token).token
        user_id = await RedisDB.get(user_token_key)
        if user_id is None:
            raise AppException(HttpResp.TOKEN_INVALID)

        # 检查用户信息是否正确
        user_info_key = RedisKey.UserInfo(user_id).info
        user_info_str = await RedisDB.get(user_info_key)
        if user_info_str is None:
            try:
                user_obj_ = await UserService().detail(ObjectId(user_id))
                role_ids = [str(role_id) for role_id in user_obj_["role_ids"]]
                user_obj_["role_ids"] = role_ids
            except Exception:
                raise AppException(HttpResp.TOKEN_INVALID)
        else:
            user_obj_ = json.loads(user_info_str)
        user_obj_["_id"] = user_id
        user = UserModel.CacheInfo(**user_obj_)
        # 检查用户状态
        assert user.status is not UserModel.Status.LOCKED.value, "User is locked"
        assert user.status is not UserModel.Status.DELETED.value, "User is deleted"
        # 缓存用户信息
        await RedisDB.set(user_info_key, user.model_dump_json(exclude={"id", "_id"}))

        # token 过期时间小于 2 小时，重新设置过期时间
        # ws的token一般是长期token，所以不需要刷新过期时间
        ttl = await RedisDB.ttl(user_token_key)
        if ttl != -1 and ttl < TOKEN_EXPIRE:
            await RedisDB.expire(user_token_key, TOKEN_EXPIRE)

        # 添加超级管理员 buff
        if UserModel.SUPER_ADMIN_ROLE_ID in user.role_ids:
            user.is_super_admin = True

        # 缓存用户信息到 request.state
        req.state.user_token = self.auth_token
        req.state.user = user

    async def ws(self, req: WebSocket):
        """WebSocket 接口认证校验，需要手动调用"""
        token = req.query_params.get("authorization")
        try:
            self.auth_type, self.auth_token = token.split()  # type: ignore
        except Exception:
            raise AppException(HttpResp.TOKEN_EMPTY)
        await self.check_user_info(req)

    async def http(self, req: Request):
        """HTTP 接口认证校验，使用注入回调"""
        url_key = req.url.path[8:].replace("/", ":")
        if is_not_auth_url(url_key):
            return None
        token = req.headers.get("authorization")
        try:
            self.auth_type, self.auth_token = token.split()  # type: ignore
        except Exception:
            raise AppException(HttpResp.TOKEN_EMPTY)
        await self.check_user_info(req)

    async def __call__(self, req: Request):
        """注入回调"""
        await self.http(req)


class CheckPermission:
    """检查权限"""

    def __init__(self, func_ids: List[FuncId], role_flag: Optional[RoleResourceFlag]):
        self.need_func_ids = set(func_ids)
        self.need_role_flag = role_flag

    def parse_resources_id(self, req: Request) -> Tuple[Optional[ObjectId], Optional[ObjectId]]:
        """获取车辆或者操作台ID"""
        vehicle_id, opc_id = None, None
        url_keys = req.url.path[8:].strip("/").split("/")
        if len(url_keys) > 1:
            if url_keys[0] == "vehicles":
                vehicle_id = ObjectId(url_keys[1])
            if url_keys[0] == "op_consoles":
                opc_id = ObjectId(url_keys[1])
        return vehicle_id, opc_id

    async def __call__(self, req: Request) -> None:
        user = req.state.user
        if user.is_super_admin:
            return

        # 获取资源 ID
        vehicle_id, opc_id = self.parse_resources_id(req)

        # 检查权限
        if await RoleService.is_have_permissions(
            user.role_ids,
            list(self.need_func_ids),
            self.need_role_flag,
            opc_id,
            vehicle_id,
        ):
            return None

        raise AppException(HttpResp.NO_PERMISSION)


def gen_dp(func_id: FuncId, flags: Optional[RoleResourceFlag] = None) -> Any:
    """生成权限依赖"""
    return [Depends(CheckPermission([func_id], flags))]
