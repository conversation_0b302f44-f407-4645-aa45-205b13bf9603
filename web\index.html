<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/src/assets/images/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>BuilderX</title>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      .preload {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        width: 100vw;
      }

      .circular {
        height: 42px;
        width: 42px;
        animation: loading-rotate 2s linear infinite;
      }

      .circular .path {
        animation: loading-dash 1.5s ease-in-out infinite;
        stroke-dasharray: 90, 150;
        stroke-dashoffset: 0;
        stroke-width: 2;
        stroke: "var(--el-color-primary)";
        stroke-linecap: round;
      }

      @keyframes loading-rotate {
        100% {
          transform: rotate(1turn);
        }
      }

      @keyframes loading-dash {
        0% {
          stroke-dasharray: 90, 150;
          stroke-dashoffset: -40px;
        }

        100% {
          stroke-dasharray: 90, 150;
          stroke-dashoffset: -120px;
        }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="preload">
        <svg viewBox="25 25 50 50" class="circular">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      let locale = JSON.parse(localStorage.getItem("locale"));
      
      if (locale && locale.localInfo.locale === "zh-CN") {
        locale.localInfo.locale = "zh_CN";
        locale.localInfo.fallback = "zh_CN";
        localStorage.setItem("locale", JSON.stringify(locale));
      }
    </script>
  </body>
</html>
