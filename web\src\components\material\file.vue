<template>
  <div>
    <div class="file-item relative" :style="{ height: fileSize, width: fileSize }">
      <el-image class="image" v-if="type == 'image'" fit="contain" :src="url"></el-image>
      <!-- <video preload="none" class="video" v-else-if="type == 'video'" :src="url"></video> -->
      <img class="w-full h-full object-cover" :src="url" />
      <div
        v-if="type == 'video'"
        @click.stop="playVideo"
        class="play-video absolute left-1/2 top-1/2 translate-x-[-50%] translate-y-[-50%] rounded-full w-5 h-5 flex justify-center items-center bg-[rgba(0,0,0,0.3)]"
      >
        <icon name="el-icon-CaretRight" :size="18" color="#fff" />
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
  props: {
    // 图片地址
    url: {
      type: String,
    },
    // 图片地址
    id: {
      type: String,
      default: "",
    },
    // 文件名
    name: {
      type: String,
      default: "",
    },
    // 图片尺寸
    fileSize: {
      type: String,
      default: "100px",
    },
    // 文件类型
    type: {
      type: String,
      default: "video",
    },
  },
  emits: ["play", "close"],
  setup(props, { emit }) {
    const playVideo = () => {
      emit("play", { id: props.id, stream_name: props.name });
    };
    return { playVideo };
  },
});
</script>

<style scoped lang="scss">
.file-item {
  box-sizing: border-box;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  @apply bg-br-extra-light border border-br-extra-light;

  .image,
  .video {
    display: block;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
  }
}

.play-video {
  cursor: pointer;
}

.play-video:hover {
  background-color: rgba(255, 95, 0, 0.9);
}
</style>
