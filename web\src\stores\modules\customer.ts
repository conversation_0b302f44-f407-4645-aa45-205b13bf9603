import { defineStore } from "pinia";
import { phenomenonList as phenomenonList<PERSON>pi, operationList as operation<PERSON>ist<PERSON><PERSON> } from "@/api/customer";

interface CustomerState {
  phenomenonList: any[];
  operationList: any[];
}

const useCustomerStore = defineStore({
  id: "CustomerStore",
  state: (): CustomerState => ({
    phenomenonList: [],
    operationList: [],
  }),
  actions: {
    getPhenomenonList() {
      return new Promise((resolve, reject) => {
        phenomenonListApi({})
          .then(async (data) => {
            this.phenomenonList = data.lists;
            resolve(data.lists);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getOperationList() {
      return new Promise((resolve, reject) => {
        operationListApi({ page_no: 1, page_size: 99 })
          .then(async (data) => {
            this.operationList = data.lists;
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
  persist: [
    // {
    //   pick: ["historyList", "sessionList", "phenomenonList", "operationList"],
    //   storage: localStorage,
    // },
  ],
});

export default useCustomerStore;
