<template>
  <popup
    ref="popupRef"
    :title="popupTitle"
    :async="true"
    width="600px"
    @confirm="handleSubmit"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入姓名"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="formData.phone"
          placeholder="请输入手机号"
          maxlength="11"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="会员卡类型" prop="card_type">
        <el-radio-group v-model="formData.card_type">
          <el-radio value="年卡">年卡</el-radio>
          <el-radio value="次卡">次卡</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="到期时间" prop="expire_time">
        <el-date-picker
          v-model="formData.expire_time"
          type="datetime"
          placeholder="请选择到期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="游戏币余额" prop="coin_balance">
        <el-input-number
          v-model="formData.coin_balance"
          :min="0"
          :precision="2"
          placeholder="请输入游戏币余额"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
  </popup>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import Popup from "@/components/popup/index.vue"
import feedback from "@/utils/feedback"
import { memberAdd, memberEdit } from "@/api/member"

const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')
const popupTitle = computed(() => {
  return mode.value === 'edit' ? '编辑会员' : '新增会员'
})

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  phone: '',
  card_type: '年卡',
  expire_time: '',
  coin_balance: 0,
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '姓名长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  card_type: [
    { required: true, message: '请选择会员卡类型', trigger: 'change' }
  ],
  expire_time: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && new Date(value) <= new Date()) {
          callback(new Error('到期时间不能早于当前时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  coin_balance: [
    { required: true, message: '请输入游戏币余额', trigger: 'blur' },
    { type: 'number', min: 0, message: '游戏币余额不能小于0', trigger: 'blur' }
  ]
}

// 打开弹窗
const open = (data?: any) => {
  mode.value = data ? 'edit' : 'add'
  
  if (data) {
    // 编辑模式，填充数据
    Object.assign(formData, {
      id: data.id,
      name: data.name,
      phone: data.phone,
      card_type: data.card_type,
      expire_time: data.expire_time || '',
      coin_balance: data.coin_balance || 0,
    })
  } else {
    // 新增模式，重置表单
    Object.assign(formData, {
      id: '',
      name: '',
      phone: '',
      card_type: '年卡',
      expire_time: '',
      coin_balance: 0,
    })
  }
  
  popupRef.value?.open()
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    
    const submitData = { ...formData }
    
    // 如果没有设置到期时间，则删除该字段
    if (!submitData.expire_time) {
      delete submitData.expire_time
    }
    
    if (mode.value === 'edit') {
      await memberEdit(submitData)
      feedback.msgSuccess('编辑成功')
    } else {
      const { id, ...createData } = submitData
      await memberAdd(createData)
      feedback.msgSuccess('新增成功')
    }
    
    emit('success')
    return true
  } catch (error) {
    console.error('表单提交失败:', error)
    return false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
