<!-- 系统日志 -->
<template>
  <div class="journal">
    <el-card id="search" class="!border-none" shadow="never">
      <el-form class="ls-form" :model="formData" inline>
        <el-form-item label="用户账号">
          <el-input class="w-[180px]" placeholder="请输入" v-model="formData.user_name" clearable />
        </el-form-item>

        <el-form-item label="请求方式">
          <el-select style="width: 180px" v-model="formData.method" placeholder="请选择">
            <el-option v-for="(item, index) in visitType" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="日志时间">
          <daterange-picker style="width: 300px" v-model:startTime="startTime" v-model:endTime="endTime" />
        </el-form-item>

        <el-form-item label="请求链接">
          <el-input class="w-[180px]" placeholder="请输入" v-model="formData.path" clearable @keyup.enter="resetPage" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="resetPageFormat">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="!border-none mt-4" shadow="never" v-loading="pager.loading">
      <div>
        <el-table :height="tableHeight" :data="pager.lists" size="large">
          <el-table-column label="用户账号" prop="username" />
          <el-table-column label="用户名称" prop="nickname" />
          <el-table-column label="访问链接" prop="path" />
          <el-table-column label="访问方式" prop="method" />
          <el-table-column label="访问ip" prop="ip" />
          <el-table-column label="信息" prop="msg" />
          <el-table-column label="日志时间" prop="created_time">
            <template #default="{ row }">
              {{ dayjs(row.created_time).local().format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="updated_time">
            <template #default="{ row }">
              <el-button type="primary" size="small" link @click="handleDetail(row)">详情 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>

    <audit-detail ref="detailRef" v-if="isShowDetail" :formData="curRow" @close="isShowDetail = false" />
  </div>
</template>

<script setup lang="ts" name="journal">
import { getAuditLogList } from "@/api/setting/system";
import { usePaging } from "@/hooks/usePaging";
import { formatDate2ISO } from "@/utils/util";

import auditDetail from "./audit-detail.vue";

// 查询表单
const formData = ref({
  user_name: "",
  path: "",
  method: "",
  start_time: "",
  end_time: "",
});

const startTime = ref("");
const endTime = ref("");
const tableHeight = ref(0);

const curRow = ref({});
const detailRef = shallowRef<InstanceType<typeof auditDetail>>();
const isShowDetail = ref(false);

// 访问方式
const visitType = ref<Array<any>>([
  {
    label: "全部",
    value: "",
  },
  {
    label: "GET",
    value: "GET",
  },
  {
    label: "POST",
    value: "POST",
  },
  {
    label: "PUT",
    value: "PUT",
  },
  {
    label: "DELETE",
    value: "DELETE",
  },
]);

const calcTableHeight = () => {
  // 获取id search的高度
  const searchHeight = document.getElementById("search")?.clientHeight || 0;
  tableHeight.value = window.innerHeight - searchHeight - 50 - 64 - 48 - 16;
};

onMounted(() => {
  calcTableHeight();
  window.addEventListener('resize', calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', calcTableHeight);
});

const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: getAuditLogList,
  params: formData.value,
});

const resetPageFormat = () => {
  if (startTime.value) {
    formData.value.start_time = formatDate2ISO(startTime.value);
    formData.value.end_time = formatDate2ISO(endTime.value);
  }
  resetPage();
};

getLists();

const handleDetail = async (row: any) => {
  curRow.value = row;
  isShowDetail.value = true;
  await nextTick();
  detailRef.value?.open();
};
</script>

<style lang="scss" scoped></style>
