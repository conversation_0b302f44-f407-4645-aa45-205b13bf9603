<template>
  <div class="edit-popup">
    <el-drawer v-model="drawerShow" title="菜单上传" :close-on-click-modal="false" :size="650">
      <div class="h-full flex flex-col">
        <JsonEditorVue
          v-if="drawerShow"
          class="w-[580px] ml-4"
          style="height: calc(100% - 50px)"
          v-model="params"
          mode="text"
          :mainMenuBar="false"
          :onChange="onJsonSave"
        />
        <div class="flex flex-grow justify-end items-end mt-4 h-8">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import feedback from "@/utils/feedback";
import JsonEditorVue from "json-editor-vue";
import { menuUpload } from "@/api/perms/menu";
import { countDown } from "@/utils/util";

const props = defineProps({
  carId: {
    type: String,
    require: true,
    default: "",
  },
  menuJson: {
    type: Array,
    require: true,
    default: [],
  },
});

const pageData: any = reactive({
  id: "",
  menuJson: [],
});

const emit = defineEmits(["success", "close"]);
const drawerShow = ref(false);
const params: any = ref([]);

const onJsonSave = (value: any) => {
  pageData.menuJson = JSON.parse(value.text);
};

const handleSubmit = async () => {
  let obj = pageData.menuJson;

  if (pageData.menuJson.length === 0) {
    obj = params.value;
  }

  if (obj.length === 0 || obj === null) return feedback.msgWarning("请上传正确菜单");

  await menuUpload(obj);
  feedback.msgSuccess("操作成功");
  handleClose();
  countDown();
};

const open = () => {
  drawerShow.value = true;
  params.value = props.menuJson;
};

const handleClose = () => {
  params.value = [];
  drawerShow.value = false;
};

defineExpose({
  open,
});
</script>
