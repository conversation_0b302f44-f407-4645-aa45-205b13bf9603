from cgi import print_arguments
from itertools import count
import arrow
from bson import ObjectId
from apps.db.mongo_db import MongoDB
import apps.models.guide_man as GuideModel
import apps.services.guide_man as GuideService
from pymongo.errors import DuplicateKeyError
from apps.common import AppException, HttpResp
from apps.services.vehicles import query


COLL_GUIDE = MongoDB.get_collection("work_guide")


async def guide_list(query: GuideModel.GuideQuery):
    """获取作业指导列表"""
    filter = {"name": query.name}
    if query.name == None:
        filter = {}
    results = await COLL_GUIDE.find(filter).to_list(length=None)
    count = await COLL_GUIDE.count_documents(filter)
    return {"count": count, "lists": [GuideModel.GuideOut(**item) for item in results]}


async def guide_create(guide: GuideModel.GuideCreate):
    """创建作业指导"""
    guide_dict = guide.model_dump()
    guide_dict.update(
        {
            "content": [],
            "create_time": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
            "update_time": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
        }
    )
    # 检查作业指导是否存在
    filter = {"name": guide.name}
    if await COLL_GUIDE.count_documents(filter) > 0:
        raise AppException(HttpResp.GUIDE_EXISTS)
    try:
        await COLL_GUIDE.insert_one(guide_dict)
    except DuplicateKeyError:
        raise AppException(HttpResp.GUIDE_EXISTS)
    return {"msg": "作业指导创建成功"}


async def guide_update(guide_id, guide: GuideModel.GuideCreate):
    """更新作业指导"""
    guide_dict = guide.model_dump()
    guide_dict.update(
        {
            "update_time": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
        }
    )
    try:
        await COLL_GUIDE.update_one({"_id": ObjectId(guide_id)}, {"$set": guide_dict})
    except DuplicateKeyError:
        raise AppException(HttpResp.GUIDE_EXISTS)
    return {"msg": "作业指导更新成功"}


async def guide_delete(guide_id: str):
    """删除作业指导"""
    await COLL_GUIDE.delete_one({"_id": ObjectId(guide_id)})
    return {"msg": "作业指导删除成功"}


async def guide_detail(guide_id: str):
    """获取作业指导详情"""
    guide = await COLL_GUIDE.find_one({"_id": ObjectId(guide_id)})
    return GuideModel.GuideOut(**guide)


async def guide_item_create(guide_id: str, item: GuideModel.GuideItemCreate):
    """创建作业指导项"""

    pipeline = [
        {"$match": {"_id": ObjectId(guide_id)}},
        {"$project": {"content_count": {"$size": "$content"}, "max_id": {"$max": "$content.id"}}},
    ]

    cursor = COLL_GUIDE.aggregate(pipeline)
    async for document in cursor:
        content_count = document.get("content_count", 0)
        max_id = document.get("max_id", -1)
        new_id = 0 if content_count == 0 else max_id + 1

    item_dict = item.model_dump()
    item_dict.update(
        {
            "id": new_id,
            "create_time": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
            "update_time": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
        }
    )
    try:
        await COLL_GUIDE.update_one({"_id": ObjectId(guide_id)}, {"$push": {"content": item_dict}})
    except DuplicateKeyError:
        raise AppException(HttpResp.GUIDE_ITEM_EXISTS)
    return {"msg": "作业指导项创建成功"}

async def guide_item_update(guide_id: str, item_id: int, item: GuideModel.GuideItemUpdate):
    """更新作业指导项"""
    item_dict = item.model_dump()
    item_dict.update(
        {
            "update_time": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
        }
    )
    try:
        await COLL_GUIDE.update_one({"_id": ObjectId(guide_id), "content.id": item_id}, {"$set": {"content.$": item_dict}})
    except DuplicateKeyError:
        raise AppException(HttpResp.GUIDE_ITEM_EXISTS)
    return {"msg": "作业指导项更新成功"}


async def guide_item_delete(guide_id: str, item_id: int):
    """删除作业指导项"""
    await COLL_GUIDE.update_one({"_id": ObjectId(guide_id)}, {"$pull": {"content": {"id": item_id}}})
    return {"msg": "作业指导项删除成功"}