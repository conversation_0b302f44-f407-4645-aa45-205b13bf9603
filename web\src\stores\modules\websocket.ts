import { defineStore, storeToRefs } from "pinia";
import feedback from "@/utils/feedback";
import useUserStore from "@/stores/modules/user";
import useNetworkStore from "@/stores/modules/network";
import { timeFormat } from "@/utils/util";
interface WebsocketState {
  ws: any;
  isConnect: boolean;
  heartbeatTimer: any;
  heartbeatInterval: number;
  reconnectTimer: any;
  reconnectCount: number;
  startTime: number;
  pingList: number[];
  nodeStatus: any;
  vehicleInfo: any;
  vehicleControl: any;
  controlChartData: any[];
}

const useWebsocketStore = defineStore({
  id: "websocket",
  state: (): WebsocketState => ({
    ws: null,
    isConnect: false,
    heartbeatTimer: null, // 心跳timer
    reconnectTimer: null, // 重连timer
    heartbeatInterval: 5000, // 心跳发送频率
    reconnectCount: 0, // 重连次数
    startTime: 0,
    pingList: [],
    nodeStatus: {},
    vehicleInfo: {
      emergency_brake_status: 0, // 紧急刹车状态
      console_emergency_stop_status: 0, // 控制台紧急停止状态
      remote_control_enable_status: 2, // 远程控制启用状态
      local_remote_emergency_stop_status: 0, // 本地远程紧急停止状态
      function_button_status: 4352, // 功能按钮状态
      fuel_level: 0, // 油量
      fuel_delivery_pressure: 0, // 燃油输送压力
      coolant_level: 0, // 冷却液水平
      engine_working_hours: 0, // 发动机工作小时数
      left_main_pump_pressure: 0, // 左主泵压力
      right_main_pump_pressure: 0, // 右主泵压力

      oil_pressure: 404, // 油压
      battery_voltage: 28.1, // 电池电压

      water_temperature: 59, // 冷却液温度
      ambient_temperature: 2.3, // 环境温度
      hydraulic_oil_temperature: 25, // 液压油温度

      engine_rpm: 800, // 发动机转速
      oil_temperature: 0, // 油温
      cab_temperature: 0, // 驾驶室温度 ? 不显示
    },
    vehicleControl: {
      left_joystick_x: 0,
      left_joystick_y: 0,
      left_joystick_z_or_kx: 0,
      left_joystick_ky: 0,
      right_joystick_x: 0,
      right_joystick_y: 0,
      right_joystick_z_or_kx: 0,
      right_joystick_ky: 0,
      left_pedal: 0,
      right_pedal: 0,
    },
    controlChartData: [
      {
        name: new Date().toString(),
        value: [timeFormat(null, "yyyy-mm-dd hh:MM:ss"), 0],
      },
    ],
  }),
  actions: {
    init() {
      if (!("WebSocket" in window)) {
        feedback.msgWarning("浏览器不支持WebSocket");
        return null;
      }
      const userStore = useUserStore();
      const networkStore = useNetworkStore();
      const { webPing } = storeToRefs(networkStore);

      let hostUrl = import.meta.env.VITE_APP_SOCKET_URL;
      if (import.meta.env.PROD) hostUrl = window.document.location.host;

      let protocol = "ws";
      if (location.protocol === "https:") protocol = "wss";

      const wsUrl = `${protocol}://${hostUrl}/api/v1/ws/user?authorization=${userStore.token}`;

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        // feedback.msgSuccess("WebSocket连接成功");
        this.isConnect = true;
        // this.heartbeat();
      };

      this.ws.onmessage = (event: any) => {
        const res = JSON.parse(event.data);
        if (res.type === "ping_web") {
          const { ping, jitter } = networkDetection();

          webPing.value.ping.push({
            name: new Date().toString(),
            value: [timeFormat(null, "yyyy-mm-dd hh:MM:ss"), ping],
          });
          webPing.value.jitter.push({
            name: new Date().toString(),
            value: [timeFormat(null, "yyyy-mm-dd hh:MM:ss"), jitter],
          });

          // console.log("ping", ping, "jitter", jitter);
        }

        if (res.cmd === "alarm.ros" || res.cmd === "alarm.system") {
          if (res.data.level === "error") {
            feedback.notifyError(res.data.msg);
          } else {
            feedback.notifySuccess(res.data.msg);
          }
        }

        if (res.cmd === "status.ros_node") {
          let id = res.room_id.split(".")[1];
          const data = Object.entries(res.data).reduce((acc: any[], [key]) => {
            acc.push({ name: key, value: res.data[key]?.is_live ? 1 : 0 });
            return acc;
          }, []);
          this.nodeStatus[id] = data;
        }

        if (res.cmd === "status.vehicle.info") {
          for (const key in this.vehicleInfo) {
            if (res.data[key] != null && res.data[key] != undefined) {
              this.vehicleInfo[key] = res.data[key];
            }
          }
        }

        if (res.cmd === "status.vehicle.control") {
          for (const key in this.vehicleControl) {
            if (res.data[key] != null && res.data[key] != undefined) {
              this.vehicleControl[key] = res.data[key];
            }
          }

          const sumOfAbsoluteValues = Object.values(this.vehicleControl)
            .map((item: any) => Math.abs(item))
            .reduce((sum: any, current: any) => sum + current, 0);

          this.controlChartData.push({
            name: new Date().toString(),
            value: [timeFormat(null, "yyyy-mm-dd hh:MM:ss"), sumOfAbsoluteValues || 0],
          });
        }

        if (res.cmd === "alarm.info") {
          // feedback.msgSuccess("数据获取成功");
        }
      };

      this.ws.onclose = () => {
        // feedback.msgWarning("WebSocket连接关闭");
        this.reconnect();
      };

      this.ws.onerror = () => {
        // feedback.msgError("WebSocket连接失败");
      };

      const networkDetection = () => {
        const endTime = new Date().getTime();
        let ping = endTime - this.startTime;
        let jitter = 0;
        this.pingList.push(ping);
        if (this.pingList.length > 2) {
          this.pingList = this.pingList.slice(-6);
          jitter = Math.max(...this.pingList) - Math.min(...this.pingList);
        }
        return { ping, jitter };
      };
    },

    send(data: any) {
      if (this.ws && this.isConnect) {
        this.ws.send(JSON.stringify(data));
      } else {
        feedback.msgWarning("WebSocket未连接");
      }
    },

    heartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
      }
      this.heartbeatTimer = setInterval(() => {
        this.startTime = new Date().getTime();

        let data = {
          content: "ping_web",
        };
        this.ws.send(JSON.stringify(data));
      }, this.heartbeatInterval);
    },

    reconnect() {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      if (this.reconnectCount > 3) {
        return;
      } else {
        this.reconnectTimer = setTimeout(() => {
          this.reconnectCount++;
          // feedback.msgWarning("WebSocket正在重连...");
          this.init();
        }, 15000);
      }
    },
  },
});

export default useWebsocketStore;
