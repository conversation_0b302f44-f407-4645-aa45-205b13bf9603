<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="550px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="车辆名称" prop="vehicle_name">
          <el-input v-model="formData.vehicle_name" placeholder="请输入车辆名称" clearable />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "vehicle.编辑操作台" : "vehicle.添加操作台";
});

const formData: any = reactive({
  id: "",
});

const formRules = reactive({
  vehicle_name: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
});

onMounted(() => {});

const handleSubmit = async () => {
  await formRef.value?.validate();
  // mode.value == "edit" ? await consoleUpdate(formData) : await consoleAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (data: any) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
