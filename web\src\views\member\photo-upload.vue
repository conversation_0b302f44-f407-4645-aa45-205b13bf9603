<template>
  <popup
    ref="popupRef"
    title="上传会员照片"
    :async="true"
    width="500px"
    @confirm="handleSubmit"
    @close="handleClose"
  >
    <div class="photo-upload-container">
      <div class="current-photo" v-if="memberData.photo">
        <div class="photo-label">当前照片：</div>
        <el-image
          :src="memberData.photo"
          fit="cover"
          class="current-photo-img"
          :preview-src-list="[memberData.photo]"
        />
      </div>
      
      <div class="upload-section">
        <div class="photo-label">上传新照片：</div>
        <el-upload
          ref="uploadRef"
          class="photo-uploader"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :show-file-list="false"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          accept="image/*"
        >
          <div class="upload-area">
            <el-image
              v-if="previewUrl"
              :src="previewUrl"
              fit="cover"
              class="preview-img"
            />
            <div v-else class="upload-placeholder">
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">点击上传照片</div>
            </div>
          </div>
        </el-upload>
        
        <div class="upload-tips">
          <p>• 支持 JPG、PNG、GIF 格式</p>
          <p>• 文件大小不超过 5MB</p>
          <p>• 建议尺寸：200x200 像素</p>
        </div>
      </div>
    </div>
  </popup>
</template>

<script lang="ts" setup>
import { Plus } from '@element-plus/icons-vue'
import type { UploadProps } from 'element-plus'
import Popup from "@/components/popup/index.vue"
import feedback from "@/utils/feedback"
import config from "@/config"
import useUserStore from "@/stores/modules/user"

const emit = defineEmits(['success', 'close'])
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const uploadRef = shallowRef()
const userStore = useUserStore()

// 会员数据
const memberData = reactive({
  id: '',
  name: '',
  photo: ''
})

// 预览图片URL
const previewUrl = ref('')
const uploadedPhotoUrl = ref('')

// 上传配置
const uploadAction = `${config.baseUrl}${config.urlPrefix}/v1/member/upload_photo`
const uploadHeaders = computed(() => ({
  authorization: userStore.token,
  version: config.version,
}))
const uploadData = computed(() => ({
  member_id: memberData.id
}))

// 打开弹窗
const open = (data: any) => {
  Object.assign(memberData, {
    id: data.id,
    name: data.name,
    photo: data.photo || ''
  })
  
  // 重置状态
  previewUrl.value = ''
  uploadedPhotoUrl.value = ''
  
  popupRef.value?.open()
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
}

// 上传前验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    feedback.msgError('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    feedback.msgError('图片大小不能超过 5MB!')
    return false
  }

  // 创建预览URL
  const reader = new FileReader()
  reader.onload = (e) => {
    previewUrl.value = e.target?.result as string
  }
  reader.readAsDataURL(file)

  return true
}

// 上传成功
const handleUploadSuccess = (response: any) => {
  if (response.code === 200) {
    uploadedPhotoUrl.value = response.data.photo_url
    feedback.msgSuccess('照片上传成功')
  } else {
    feedback.msgError(response.msg || '上传失败')
  }
}

// 上传失败
const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  feedback.msgError('照片上传失败，请重试')
}

// 提交
const handleSubmit = async () => {
  if (!uploadedPhotoUrl.value) {
    feedback.msgError('请先上传照片')
    return false
  }
  
  emit('success')
  return true
}

defineExpose({
  open
})
</script>

<style scoped>
.photo-upload-container {
  padding: 20px 0;
}

.current-photo {
  margin-bottom: 30px;
}

.photo-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 10px;
}

.current-photo-img {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.upload-section {
  margin-top: 20px;
}

.photo-uploader {
  display: block;
}

.upload-area {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.preview-img {
  width: 100%;
  height: 100%;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
}

.upload-tips {
  margin-top: 15px;
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

.upload-tips p {
  margin: 0;
}
</style>
