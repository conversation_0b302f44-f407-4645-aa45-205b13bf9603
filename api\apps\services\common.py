import json
from typing import Union

import arrow
from fastapi import Request, WebSocket


def get_header_info(req: Union[WebSocket, Request]):
    # 获取请求头中应当携带的设备信息
    system_info = json.loads(req.headers.get("sysinfo", "{}"))
    ip = req.headers.get("X-Forwarded-For", "")
    if ip == "" and req.client is not None:
        ip = req.client.host
    system_info["ip"] = ip
    system_info["online_time"] = arrow.utcnow().datetime.isoformat()
    return system_info
