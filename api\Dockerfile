FROM python:3.10-alpine

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN addgroup -S builderx && adduser -S builderx -G builderx

WORKDIR /app

# 安装 poetry
RUN pip install poetry

# 复制依赖定义文件
COPY pyproject.toml poetry.lock* ./

# 安装依赖
# --no-root: 不安装项目本身，只安装依赖
# --no-dev: 不安装开发依赖
RUN poetry config virtualenvs.create false && poetry install --no-root --without dev

# 复制后端代码
COPY . .

# 创建静态文件目录
RUN mkdir -p static

USER builderx

EXPOSE 8800

CMD ["poetry", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8800"]