# 统一运维管控平台

## 1. 项目介绍
该项目模块主要用于，主要包括以下功能：
* 1.1. 

## 2. 项目结构


## 3. 项目部署
### 3.1. 项目环境
* Python >=3.8
* MongoDB >=4.4.1
* Redis >=6.0.9
* Docker >=19.03.13
* Docker-compose >=1.27.4

### 3.2 开发部署运行
``` bash
# 1. 使用poetry管理项目
(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -

# 2. 安装依赖
poetry install

# 3. 进入虚拟环境
poetry shell

# 4. 运行脚本
poetry run python main.py

# 5. 运行项目(在项目的虚拟环境中执行 uvicorn)
poetry run uvicorn main:app --reload --host 127.0.0.1 --port 8000 

# 以下为旧版安装依赖方式
pip install -r requirement.txt

uvicorn main:app --reload --host 0.0.0.0 --port 8000

将项目部署到Docker中
docker-compose build
启动项目
docker-compose up -d  以后台方式启动  不加 -d则是以前台方式启动
# 停止项目
docker-compose down

# 修改文件后需重新构建
docker-compose build

# 查看项目实时日志
docker logs -f 容器id

# 进入容器
docker exec -it api-1 /bin/sh

```

## 4. API接口
接口文档请参考：[API接口文档]

