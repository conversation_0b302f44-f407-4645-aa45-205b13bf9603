from influxdb_client.client.query_api import QueryOptions
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync

from apps.config import get_settings
from apps.utils.patterns import singleton


__all__ = ["InfluxDB"]


@singleton
class InfluxDB:
    host = get_settings().influxdb_host
    port = get_settings().influxdb_port
    uri = f"http://{host}:{port}"
    org = get_settings().influxdb_org
    token = get_settings().influxdb_admin_token
    # influxdb_client = InfluxDBClientAsync(url=uri, token=token, org=org)

    def __init__(self, _=None) -> None:
        self.client = InfluxDBClientAsync(url=self.uri, token=self.token, org=self.org)
        self.query_api = self.client.query_api()

    def query(self, query):
        """
        Read data from the InfluxDB database.

        Parameters:
            query (str): The query to retrieve data.

        Returns:
            data (list): The retrieved data.
        """
        return self.query_api.query(query)

    def query_stream(self, query):
        """
        Read data from the InfluxDB database.

        Parameters:
            query (str): The query to retrieve data.

        Returns:
            data (list): The retrieved data.
        """
        return self.query_api.query_stream(query)
