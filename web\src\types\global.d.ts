import { Dayjs } from "dayjs";
import "vue-i18n";
import { DefinedLocaleMessage } from "vue-i18n";

declare module "@vue/runtime-core" {
  interface ComponentCustomProperties {
    dayjs: typeof import("dayjs");
  }
}

declare module "vue-i18n" {
  export interface DefineLocaleMessage extends DefinedLocaleMessage {}
  export interface VueMessageType extends DefineLocaleMessage {}
}

declare module "@vue/runtime-core" {
  interface ComponentCustomProperties {
    $t(key: string, ...args: unknown[]): string;
    $d(value: Date | number, ...args: unknown[]): string;
  }
}
