<template>
  <div class="role-lists">
    <el-card class="!border-none" shadow="never">
      <div>
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新增
        </el-button>
      </div>
      <div class="mt-4">
        <div>
          <el-table :height="calcTableHeight()" :data="pager.lists" size="large" v-loading="pager.loading">
            <el-table-column label="ID" prop="id" min-width="100" />
            <el-table-column prop="content" label="内容" min-width="150" />
            <el-table-column prop="description" label="备注" min-width="150" show-overflow-tooltip />
            <el-table-column prop="create_time" label="创建时间" min-width="180">
              <template #default="{ row }">
                {{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </el-table-column>
            <el-table-column label="技术类型" width="110">
              <template #default="{ row }">
                <el-tag>{{ ANSWER_TYPE[row.answer_type] }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="设备类型" width="140">
              <template #default="{ row }">
                <el-tag>{{ DEVICE_TYPE[row.device_type] }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="需要工具" width="110">
              <template #default="{ row }">
                <el-tag>{{ row.is_need_tools ? "需要" : "不需要" }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="190" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleEdit(row)"> 编辑 </el-button>
                <el-button link type="danger" @click="handleDelete(row.id)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="flex justify-end mt-4">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>
<script lang="ts" setup name="role">
import { operationList, operationDelete } from "@/api/customer-ja";
import { ANSWER_TYPE, DEVICE_TYPE } from "@/utils/constants";

import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const showEdit = ref(false);
const { pager, getLists } = usePaging({
  fetchFun: operationList,
});

const calcTableHeight = () => {
  return window.innerHeight - 218;
};

const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

// 删除角色
const handleDelete = async (id: string) => {
  await feedback.confirm("本当に削除してよろしいでしょうか？");
  await operationDelete(id);
  feedback.msgSuccess("削除に成功しました");
  getLists();
};

getLists();
</script>
