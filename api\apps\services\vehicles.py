from typing import List, Union, Dict, Any, Optional

import arrow
from bson import ObjectId
from pymongo import ReturnDocument
from pymongo.errors import DuplicateKeyError

from apps.db import MongoDB, RedisDB
from apps.utils.wsbroadcast import send_msg_to_client
from apps.common import HttpResp, AppException
import apps.models.redis_key as RedisKey
import apps.models.user as UserModel
import apps.models.vehicle as VModel
import apps.models.websocket as WSModel
import apps.models.ros as RosModel
import apps.models.data_man as DModel
import apps.models.device as DeviceModel
import apps.services.role as RoleService


COLL = MongoDB.get_collection("vehicles")
COLLPARAM = MongoDB.get_collection("vehicles_parameter")
COLL_ANDROIDPARAM = MongoDB.get_collection("vehicles_android_parameter")
COLL_VIDEO_DATA = MongoDB.get_collection("video_data")
COLL_OPC_INFO = MongoDB.get_collection("op_consoles")


async def is_vehicleid_exists(vehicle_id: ObjectId):
    """根据车辆ID查询车辆是否存在"""
    result = await COLL.find_one({"_id": vehicle_id}, {"_id": 1})
    if result:
        return True
    return False


async def get_realtime_status(vehicle_id: ObjectId) -> VModel.RealTimeStatus:
    """获取车辆实时状态"""
    vkey = RedisKey.Vehicle(vehicle_id)
    v_status = VModel.RealTimeStatus()

    # 参数同步状态
    sync_code_ = await RedisDB.get(vkey.params_sync_status)
    if sync_code_ is not None:
        if int(sync_code_) == WSModel.DeviceDataSyncCode.nosync.value:
            v_status.is_synced = False

    # 在线状态
    _online_status = await RedisDB.get(vkey.online_status)
    v_status.is_online = True if _online_status else False

    # 锁定状态
    _locked_id = await RedisDB.get(vkey.locked_op_console)
    v_status.is_locked = True if _locked_id else False
    if _locked_id is None:
        return v_status
    v_status.locked_by_id = _locked_id
    opc_info = await COLL_OPC_INFO.find_one({"_id": ObjectId(_locked_id)}, {"name": 1})
    if opc_info:
        v_status.locked_by_name = opc_info["name"]

    return v_status


async def get_online_status(vehicle_id: ObjectId):
    """获取车辆在线状态"""
    vkey = RedisKey.Vehicle(vehicle_id)
    online = False
    if await RedisDB.get(vkey.online_status):
        online = True
    return online


async def get_locked_opc(vehicle_id: ObjectId) -> Optional[str]:
    """获取锁定车辆的操作台ID, 如果没有锁定则返回 None"""
    vkey = RedisKey.Vehicle(vehicle_id)
    return await RedisDB.get(vkey.locked_op_console)


async def find_by_device_id(device_id: str):
    """根据设备ID查询车辆信息"""
    return await COLL.find_one({"devices.id": device_id})


async def detail(v_id: ObjectId) -> VModel.Descript:
    """获取车辆详情"""
    vehicle_info = await COLL.find_one({"_id": v_id})
    if vehicle_info is None:
        raise AppException(HttpResp.VEHICLE_NOT_FOUND)
    r_status = await get_realtime_status(v_id)
    vehicle_info.update(r_status.model_dump())
    return VModel.Descript(**vehicle_info)


async def create(data: VModel.BaseInfo):
    """Creates a vehicle"""
    v = VModel.Create(**data.model_dump())
    v.create_time = arrow.utcnow().datetime
    v.update_time = arrow.utcnow().datetime

    try:
        result = await COLL.insert_one(v.model_dump())
    except DuplicateKeyError:
        raise AppException(HttpResp.VEHICLE_NAME_REPEAT)
    assert result.inserted_id, "Vehicle create failed."
    return {"msg": "Vehicle created successfully", "data": str(result.inserted_id)}


async def delete(v_id: ObjectId):
    """删除目标车辆，需要车辆 ID"""
    v_data = await COLL.find_one(
        {"_id": v_id},
        {"devices": 1},
    )
    assert v_data, "Vehicle does not exist."

    if v_data.get("devices"):
        raise AppException(HttpResp.VEHICLE_HAS_DEVICE)

    result = await COLL.delete_one({"_id": v_id})
    assert result.deleted_count == 1, "Vehicle delete failed."
    return {}


async def update(vehicle_id: ObjectId, v_info: VModel.Update):
    """更新车辆信息"""
    filter_d = {"_id": vehicle_id}
    update_d = {
        "vehicle_name": v_info.vehicle_name,
        "vehicle_type": v_info.vehicle_type,
        "description": v_info.description,
        "update_time": arrow.utcnow().datetime,
    }
    v_data = await COLL.find_one_and_update(
        filter_d,
        {"$set": update_d},
        {"_id": 1},
        return_document=ReturnDocument.AFTER,  # 返回更新后的文档
    )
    assert v_data, "Vehicle does not exist."
    return {}


async def query(q: VModel.Query, user: UserModel.CacheInfo) -> VModel.ListOutApi:
    """获取车辆列表"""

    filter_d: Dict[Any, Any] = {}

    if q.vehicle_name:
        filter_d.update({"vehicle_name": {"$regex": q.vehicle_name}})
    if q.vehicle_type:
        filter_d.update({"vehicle_type": q.vehicle_type})

    # 根据用户权限过滤可用车辆列表，如果是超级管理员则不过滤
    if not user.is_super_admin:
        if not user.role_ids:
            return VModel.ListOutApi()
        v_ids = await RoleService.get_vehicles(user.role_ids)
        if not v_ids:
            return VModel.ListOutApi()
        filter_d.update({"_id": {"$in": v_ids}})

    vehicle_count = await COLL.count_documents(filter_d)
    if vehicle_count == 0:
        return VModel.ListOutApi()

    projection = {
        "_id": 1,
        "vehicle_name": 1,
        "vehicle_type": 1,
        "devices": 1,
        "create_time": 1,
        "update_time": 1,
    }

    limit = q.page_size
    skip_no = (q.page_no - 1) * q.page_size
    results = COLL.find(filter_d, projection).skip(skip_no).limit(limit)
    vehicle_list = []
    async for result in results:
        r_status = await get_realtime_status(result["_id"])
        result.update(r_status.model_dump())
        v = VModel.Descript(**result)
        vehicle_list.append(v)
    return VModel.ListOutApi(count=vehicle_count, lists=vehicle_list)


async def unregister_device_list():
    """获取未注册设备列表"""
    device_list = await RedisDB.zget(RedisKey.VehicleDevice.un_register_set)
    res_data = []
    for device_id in device_list:
        vdkey = RedisKey.VehicleDevice(device_id)
        device_info = await RedisDB.hgetall(vdkey.status)
        if device_info:
            device_info["device_id"] = device_id
            res_data.append(DeviceModel.UnregisteredDeviceInfo(**device_info))
        else:
            await RedisDB.zrem(RedisKey.VehicleDevice.un_register_set, device_id)
    return {"count": len(res_data), "lists": res_data}


async def bind_device(vehicle_id: ObjectId, device_id: str):
    """绑定设备, 将设备 ID 添加到车辆的 devices 字段列表中"""
    index_ = await RedisDB.zkindex(RedisKey.VehicleDevice.un_register_set, device_id)
    if index_ is None:
        raise AppException(HttpResp.DEVICE_NOT_FOUND)

    vdkey = RedisKey.VehicleDevice(device_id)
    device_status = await RedisDB.hgetall(vdkey.status)
    if device_status is None:
        raise AppException(HttpResp.DEVICE_NOT_FOUND)

    # 保存设备信息到车辆信息中
    device = VModel.DeviceInfo(
        id=device_id,
        name=device_status.get("device_name", ""),
    )
    try:
        await COLL.update_one(
            {"_id": vehicle_id, "devices.id": {"$ne": device.id}},
            {"$push": {"devices": device.model_dump()}},
            upsert=True,
        )
    except DuplicateKeyError:
        raise AppException(HttpResp.DEVICE_ID_EXISTS)

    await RedisDB.zrem(RedisKey.VehicleDevice.un_register_set, device_id)
    return {"msg": "success"}


async def get_run_params(vehicle_id: ObjectId):
    """根据设备 ID 查询设备信息"""
    data = await COLLPARAM.find_one(
        {"vehicle_id": vehicle_id},
        {
            "_id": 0,
            "ros_nodes": 1,
            "gmsl_cameras": 1,
            "ip_cameras": 1,
            "eth_can": 1,
        },
    )
    if data:
        return data
    return {}


async def get_topic_record_task() -> list[DModel.RecordMqttTask]:
    """获取所有需要录制topic的信息"""
    results = COLLPARAM.find(
        {"metadata.key": "record.signalling.mqtt".upper()},
        {"_id": 0, "vehicle_id": 1, "metadata.$": 1},
    )

    tasks_data: list[DModel.RecordMqttTask] = []
    async for result in results:
        v_id = str(result["vehicle_id"])
        t = DModel.RecordMqttTask(vehicle_id=v_id)
        t.topic_name_list = result["metadata"][0].get("value", []).split(",")
        tasks_data.append(t)
    return tasks_data


async def get_video_record_task() -> list[DModel.RecordVideoTask]:
    """获取所有需要录制的视频信息"""
    results = COLLPARAM.find(
        {"metadata.key": "RECORD.VIDEO.RTSP"},
        {"_id": 0, "vehicle_id": 1, "metadata": 1},
    )

    tasks_data: list[DModel.RecordVideoTask] = []
    async for result in results:
        v_id = str(result["vehicle_id"])
        t = DModel.RecordVideoTask(vehicle_id=v_id)
        for metadata_obj in result["metadata"]:
            if metadata_obj["key"] == "RECORD.VIDEO.RTSP":
                t.rtsp_uri_list = metadata_obj.get("value", "").split(",")
            if metadata_obj["key"] == "RECORD.VIDEO.SEGMENT_TIME":
                t.segment_time = int(metadata_obj.get("value", 300))
            if metadata_obj["key"] == "RECORD.VIDEO.SAVE_DAYS":
                t.save_days = int(metadata_obj.get("value", 30))
        tasks_data.append(t)
    return tasks_data


class Meta:
    """车辆元数据管理"""

    def __init__(self, vehicle_id: ObjectId):
        self.vehicle_id = vehicle_id

    async def get_all(self):
        """获取车辆元数据"""
        result = await COLLPARAM.find_one(
            {"vehicle_id": self.vehicle_id},
            {"_id": 0, "metadata": 1},
        )
        if result is None:
            raise AppException(HttpResp.METADATA_NOT_FOUND)
        return result.get("metadata", [])

    async def add(self, data: VModel.MetaData):
        """添加车辆元数据"""
        try:
            data.key = data.key.upper()  # key 统一转为大写
            result = await COLLPARAM.update_one(
                {
                    "vehicle_id": self.vehicle_id,
                    "metadata.key": {"$ne": data.key},
                },
                {"$push": {"metadata": data.model_dump()}},
                upsert=True,
            )
        except DuplicateKeyError:
            raise AppException(HttpResp.METADATA_EXISTS)

        if result.modified_count == 0 and result.upserted_id is None:
            raise AppException(HttpResp.FAILED)
        return {}

    async def query(self, key_name: str):
        """获取车辆元数据"""
        result = await COLLPARAM.find_one(
            {
                "vehicle_id": self.vehicle_id,
                "metadata.key": key_name.upper(),
            },
            {"_id": 1, "metadata.$": 1},
        )
        if result is None:
            raise AppException(HttpResp.METADATA_NOT_FOUND)
        return result["metadata"][0]

    async def update(self, data: VModel.MetaData):
        """更新车辆元数据"""
        key_name = data.key.upper()  # key 统一转为大写
        result = await COLLPARAM.update_one(
            {
                "vehicle_id": self.vehicle_id,
                "metadata.key": key_name,
            },
            {
                "$set": {
                    "metadata.$.value": data.value,
                    "metadata.$.type": data.type,
                }
            },
        )

        if result.modified_count == 0 and result.upserted_id is None:
            raise AppException(HttpResp.FAILED)
        await self.clean_cache(key_name)
        return {}

    async def delete(self, key_name: str):
        """删除车辆元数据"""
        key_name = key_name.upper()
        result = await COLLPARAM.update_one(
            {"vehicle_id": self.vehicle_id, "metadata.key": key_name},
            {"$pull": {"metadata": {"key": key_name}}},
        )
        if result.modified_count == 0:
            raise AppException(HttpResp.FAILED)
        await self.clean_cache(key_name)
        return {}

    async def clean_cache(self, key_name: str):
        """清除车辆元数据缓存"""
        vkey = RedisKey.Vehicle(self.vehicle_id)
        await RedisDB.hdel(vkey.metadata, key_name)


class ROS:
    """车辆ROS参数管理"""

    def __init__(self, vehicle_id: ObjectId):
        self.vehicle_id = vehicle_id
        self.filter_id = {"vehicle_id": self.vehicle_id}

    async def add_node(self, node: RosModel.Node):
        """添加 ros 节点"""
        try:
            await COLLPARAM.update_one(
                {
                    "vehicle_id": self.vehicle_id,
                    "ros_nodes.node_name": {"$ne": node.node_name},
                },
                {"$push": {"ros_nodes": node.model_dump()}},
                upsert=True,
            )
        except DuplicateKeyError:
            raise AppException(HttpResp.PROGRAM_NODE_EXISTS)
        msg = WSModel.VehicleMsg(cmd=WSModel.VehicleCmd.create_node.value, data=node)
        await send_msg_to_client(self.vehicle_id, msg)
        await check_device_params_sync(self.vehicle_id)
        return {}

    async def update_node_params(self, node: RosModel.UpdateParams):
        """更新 ros 节点参数"""
        result = await COLLPARAM.update_one(
            {
                "vehicle_id": self.vehicle_id,
                "ros_nodes.node_name": node.node_name,
            },
            {
                "$set": {
                    "ros_nodes.$.params": node.params,
                    "ros_nodes.$.auto_start": node.auto_start,
                }
            },
        )
        assert result.modified_count == 1, "update node params failed."
        msg = WSModel.VehicleMsg(cmd=WSModel.VehicleCmd.update_node_param.value, data=node)
        await send_msg_to_client(self.vehicle_id, msg)
        await check_device_params_sync(self.vehicle_id)
        return {}

    async def del_node(self, node_name: str):
        """删除 ros 节点"""
        result = await COLLPARAM.update_one(
            {"vehicle_id": self.vehicle_id, "ros_nodes.node_name": node_name},
            {"$pull": {"ros_nodes": {"node_name": node_name}}},
        )
        assert result.modified_count == 1, "delete program node failed."

        msg = WSModel.VehicleMsg(
            cmd=WSModel.VehicleCmd.control_node.value,
            data=RosModel.NodeCrontrol(node_name=node_name, action=RosModel.Action.DELETE),
        )
        await send_msg_to_client(self.vehicle_id, msg)
        await check_device_params_sync(self.vehicle_id)
        return {}

    async def control_node(self, node_ctrl: RosModel.NodeCrontrol):
        """控制 ros 节点"""
        result = await COLLPARAM.find_one(
            {
                "vehicle_id": self.vehicle_id,
                "ros_nodes.node_name": node_ctrl.node_name,
            },
            {"_id": 1},
        )
        if result is None:
            raise AppException(HttpResp.PROGRAM_NODE_NOT_FOUND)
        msg = WSModel.VehicleMsg(cmd=WSModel.VehicleCmd.control_node.value, data=node_ctrl)
        await send_msg_to_client(self.vehicle_id, msg)
        return {}

    async def get_all_node(self):
        """获取设备上所有节点列表"""
        filter_d = {"vehicle_id": self.vehicle_id}
        projection = {"_id": 0, "vehicle_id": 1, "ros_nodes": 1}
        result = await COLLPARAM.find_one(filter_d, projection)
        if result is None:
            return []
        return result.get("ros_nodes", [])

    async def import_nodes(self, nodes: List[RosModel.Node]):
        """导入节点"""
        error_nodes = []
        for node in nodes:
            try:
                await self.add_node(node)
            except AppException as e:
                if e.code != HttpResp.PROGRAM_NODE_EXISTS.code:
                    raise e
                error_nodes.append(node.node_name)
        if len(error_nodes) == 0:
            msg = "success"
        else:
            msg = f"node {','.join(error_nodes)} already exists."
        return {"msg": msg}


class Camera:
    """处理摄像头参数"""

    def __init__(self, vehicle_id: ObjectId):
        self.vehicle_id = vehicle_id
        self.filter_id = {"vehicle_id": self.vehicle_id}

    async def update_cam(
        self,
        camera_type: VModel.CameraType,
        camera_data: List[Union[VModel.GMSLCamera, VModel.IPCamera]],
    ):
        """更新相机配置"""
        prefix = "gmsl" if camera_type.value == 1 else "ip"
        cam_list = [cam.model_dump() for cam in camera_data]
        update_d = {f"{prefix}_cameras": cam_list}
        await COLLPARAM.find_one_and_update(self.filter_id, {"$set": update_d}, upsert=True)
        await check_device_params_sync(self.vehicle_id)
        return {}

    async def update_cam_status(self, camera_list: List[VModel.CameraStatus]):
        """更新设备上的相机状态"""
        for cam in camera_list:
            camera_type = "gmsl_cameras" if cam.id.startswith("VIDEO") else "ip_cameras"
            await COLLPARAM.update_one(
                self.filter_id,
                {"$set": {f"{camera_type}.$[elem].is_enable": cam.is_enable}},
                array_filters=[{"elem.id": cam.id}],
            )
        await check_device_params_sync(self.vehicle_id)
        return {}

    async def get_enable_cam(self):
        """获取设备上所有开启的摄像头列表"""
        projection = {"_id": 0, "device_id": 1, "gmsl_cameras": 1, "ip_cameras": 1}
        result = await COLLPARAM.find_one(self.filter_id, projection)
        if result is None:
            return []

        data = []
        camera_types = {
            "gmsl_cameras": VModel.CameraType.GMSL,
            "ip_cameras": VModel.CameraType.IP,
        }
        for cam_type, cam_enum in camera_types.items():
            for c in result.get(cam_type, []):
                if not c.get("is_enable"):
                    continue
                data.append(
                    {
                        "camera_id": c["camera_id"],
                        "camera_name": c["camera_name"],
                        "camera_type": cam_enum,
                    }
                )
        try:
            _virtual_cam_conf = await Meta(self.vehicle_id).query(key_name="virtual_cameras")
            virtual_cameras = _virtual_cam_conf.get("value", "").split(",")
        except AppException:
            virtual_cameras = []
        for c in virtual_cameras:
            data.append(
                {
                    "camera_id": c,
                    "camera_name": c,
                    "camera_type": VModel.CameraType.VIRTUAL,
                }
            )
        await check_device_params_sync(self.vehicle_id)
        return data

    async def get_all_cam(self, camera_type: VModel.CameraType) -> List[Union[VModel.GMSLCamera, VModel.IPCamera]]:
        """获取所有摄像头列表"""
        prefix = "gmsl" if camera_type.value == 1 else "ip"
        field_name = f"{prefix}_cameras"
        projection = {"_id": 0, "vehicle_id": 1, field_name: 1}
        result = await COLLPARAM.find_one(self.filter_id, projection)
        if result is None:
            return []
        cam_list = result.get(field_name, [])
        if prefix == "gmsl":
            return [VModel.GMSLCamera(**c) for c in cam_list]
        return [VModel.IPCamera(**c) for c in cam_list]

    async def get_all_cam_status(self):
        """获取设备上所有摄像头状态"""
        projection = {"_id": 0, "vehicle_id": 1, "gmsl_cameras": 1, "ip_cameras": 1}
        result = await COLLPARAM.find_one(self.filter_id, projection)
        if result is None:
            return []
        cameras = result.get("gmsl_cameras", []) + result.get("ip_cameras", [])
        res_data = [VModel.CameraStatus(**c).model_dump() for c in cameras]
        return res_data


class CAN:
    """处理CAN相关参数配置"""

    def __init__(self, vehicle_id: ObjectId):
        self.vehicle_id = vehicle_id
        self.filter_id = {"vehicle_id": self.vehicle_id}

    async def get_can(self):
        """获取设备上CAN列表"""
        projection = {"_id": 0, "vehicle_id": 1, "eth_can": 1}
        result = await COLLPARAM.find_one(self.filter_id, projection)
        if not result:
            return []
        return result.get("eth_can", [])

    async def update_can(self, can_data: List[VModel.CAN]):
        """更新设备上的CAN配置"""
        data = [cam.model_dump() for cam in can_data]
        result = await COLLPARAM.update_one(self.filter_id, {"$set": {"eth_can": data}})
        if result.modified_count == 0 and result.upserted_id is None:
            raise AppException(HttpResp.FAILED)
        await check_device_params_sync(self.vehicle_id)
        return {}


async def sync_device_params(vehicle_id: ObjectId):
    """同步设备参数"""
    data = await get_run_params(vehicle_id)
    msg = WSModel.VehicleMsg(
        user="server",
        cmd=WSModel.VehicleCmd.sync_data.value,
        data=WSModel.DeviceRunData(**data),
    )
    await send_msg_to_client(str(vehicle_id), msg)
    return {}


async def check_device_params_sync(vehicle_id: str | ObjectId):
    """发送指令检查检查触发设备检查是否同步"""
    msg = WSModel.VehicleMsg(user="server", cmd=WSModel.VehicleCmd.sync_check.value)
    await send_msg_to_client(vehicle_id, msg)


class Android:
    restart_keys = [
        "easyPlayUrl1",
        "easyPlayUrl6",
        "mqttClientID",
        "mqttHost",
        "mqttName",
        "mqttPassword",
        "mqttToAndroid",
        "mqttToExcavator",
        "mqttToAndroidPowerON",
        "androidToMqttPowerON",
        "useArduino",
        "niRenDTO",
        "needPowerOn",
        "powerQuery",
        "doubleDipAngle",
        "doubleFlashTurnLamp",
        "useUSBHandler",
        "mqttToAndroidTripartite",
        "mqttToAndroidTripartiteEx001",
        "mqttKeepLive",
        "mqttToTripartite",
        "simulatePedal",
        "haveHaiKang",
        "videoParamsSetting",
        "allowPowerOff",
        "allowPowerOn",
        "showPowerOffSwitch",
        "usesTheLeftHandle",
        "videoType",
    ]

    def __init__(self, vehicle_id: ObjectId) -> None:
        self.vehicle_id = vehicle_id

    def check_need_restart(self, old_doc: Dict[str, Any], new_doc: Dict[str, Any]):
        for k in self.restart_keys:
            if old_doc.get(k) != new_doc.get(k):
                return True
        return False

    async def get_params(self) -> VModel.AndroidParams:
        """获取设备上的 Android 所需参数"""
        params_info = await COLL_ANDROIDPARAM.find_one(
            {"vehicle_id": self.vehicle_id},
            {"_id": 0, "android": 1, "handleConfig": 1, "restart_time": 1},
        )
        if params_info is None:
            return VModel.AndroidParams(android={}, handleConfig={})
        return VModel.AndroidParams(**params_info)

    async def update_params(self, data: VModel.AndroidParams):
        """
        更新车辆上对应的 Android 所需的参数
        """
        old_doc = await COLL_ANDROIDPARAM.find_one_and_update(
            {"vehicle_id": self.vehicle_id},
            {"$set": data.model_dump()},
            {"android": 1, "handleConfig": 1},
            upsert=True,
            return_document=ReturnDocument.BEFORE,
        )
        if old_doc is None:
            is_restart = True
        else:
            old_data = VModel.AndroidParams(**old_doc)
            is_restart = self.check_need_restart(old_data.android, data.android)
        if is_restart:
            await COLL_ANDROIDPARAM.update_one(
                {"vehicle_id": self.vehicle_id},
                {
                    "$set": {"restart_time": int(arrow.utcnow().timestamp())},
                },
            )

        await self.send_update_notice()
        return {}

    async def get_full_params(self) -> Optional[Dict[Any, Any]]:
        base_info = await COLL.find_one(
            {"_id": self.vehicle_id},
            {"_id": 0, "vehicle_type": 1, "vehicle_name": 1},
        )
        if base_info is None:
            return None
        full_params: Dict[Any, Any] = {
            "vehicle_id": str(self.vehicle_id),
        }
        full_params.update(base_info)

        params_info = await self.get_params()
        if params_info is None:
            return None
        full_params.update(params_info)

        # 是否在线
        full_params["online"] = await get_online_status(self.vehicle_id)
        full_params["locked_opc"] = await get_locked_opc(self.vehicle_id)
        return full_params

    async def send_update_notice(self):
        """发送参数更新通知到操作台设备安卓"""
        vkey = RedisKey.Vehicle(str(self.vehicle_id))
        opc_id = await RedisDB.get(vkey.locked_op_console)
        if opc_id is None:
            return
        msg = WSModel.OPCMsg(
            cmd=WSModel.OPCCmd.update_params.value,
            data={"vehicle_id": str(self.vehicle_id)},
        )
        await send_msg_to_client(opc_id, msg)


class MixTemplate:
    """混流模版调整"""

    def __init__(self, vehicle_id: ObjectId) -> None:
        self.vehicle_id = vehicle_id

    async def add(self, templ: VModel.MixLayoutTemplate):
        # 新增模版
        try:
            result = await COLLPARAM.find_one_and_update(
                {
                    "vehicle_id": self.vehicle_id,
                    "mix_template.name": {"$ne": templ.name},
                },
                {"$push": {"mix_template": templ.model_dump()}},
                {"_id": 0, "vehicle_id": 1, "mix_template": 1},
                upsert=True,
                return_document=ReturnDocument.AFTER,
            )
        except DuplicateKeyError:
            raise AppException(HttpResp.TEMPLATE_NAME_EXISTS)
        if result is None:
            raise AppException(HttpResp.FAILED)
        return result["mix_template"]

    async def delete(self, templ_name: str):
        # 删除模版
        result = await COLLPARAM.update_one(
            {"vehicle_id": self.vehicle_id, "mix_template.name": templ_name},
            {"$pull": {"mix_template": {"name": templ_name}}},
        )
        assert result.modified_count == 1, "template delete failed"
        return {}

    async def update(self, templ: VModel.MixLayoutTemplate):
        # 更新模版
        result = await COLLPARAM.find_one_and_update(
            {"vehicle_id": self.vehicle_id, "mix_template.name": templ.name},
            {"$set": {"mix_template.$.camera_list": templ.model_dump()["camera_list"]}},
            {"_id": 1, "mix_template": 1},
        )
        if result is None:
            return AppException(HttpResp.TEMPLATE_NOT_FOUND)
        return templ.model_dump()

    async def query(self):
        # 获取本车辆混流模版列表
        result = await COLLPARAM.find_one(
            {"vehicle_id": self.vehicle_id},
            {"_id": 0, "vehicle_id": 1, "mix_template": 1},
        )
        if not result:
            return []
        return result.get("mix_template", [])


async def get_mix_layout(vehicle_id: ObjectId, index: int = 0):
    node_name = f"real_time_composite{index}"
    result = await COLLPARAM.find_one(
        {
            "vehicle_id": vehicle_id,
            "ros_nodes.node_name": node_name,
        },
        {"_id": 0, "ros_nodes.$": 1},
    )
    if result is None:
        return {}
    try:
        cam_layout = result["ros_nodes"][0]["params"]["camera_list"]
    except IndexError:
        return {}
    return cam_layout


async def update_mix_layout(vehicle_id: ObjectId, data: VModel.MixLayout, verify_cam=False):
    """更新当前混流布局配置，这里只会修改 camera_list 字段，其他配置不会覆盖"""
    node_name = f"real_time_composite{data.index}"
    cams = data.model_dump()["camera_list"]

    # 检查相机是否合法
    if verify_cam:
        ready_cams = await Camera(vehicle_id).get_all_cam_status()
        _cam_num = len(cams)
        ready_cam_ids = {cam["camera_id"] for cam in ready_cams}
        _cam_num -= sum(1 for _cam in data.camera_list.values() if _cam.device in ready_cam_ids)
        if _cam_num != 0:
            raise AppException(HttpResp.CAMERA_NOT_FOUND)

    old_doc = await COLLPARAM.find_one_and_update(
        {
            "vehicle_id": vehicle_id,
            "ros_nodes.node_name": node_name,
        },
        {
            "$set": {
                "ros_nodes.$.params.camera_list": cams,
            }
        },
        {"_id": 0, "ros_nodes.$": 1},
    )
    if not old_doc:
        raise AppException(HttpResp.FAILED)
    node_info = old_doc["ros_nodes"][0]
    node_info["params"]["camera_list"] = cams
    msg = WSModel.VehicleMsg(
        cmd=WSModel.VehicleCmd.update_node_param.value,
        data=RosModel.UpdateParams(**node_info),
    )
    await send_msg_to_client(vehicle_id, msg)
    await check_device_params_sync(vehicle_id)
    return {}


async def get_hls_url(vehicle_id: ObjectId) -> Union[str, None]:
    """获取车辆的 HLS 播放地址"""
    vkey = RedisKey.Vehicle(vehicle_id)
    url = await RedisDB.hget(vkey.metadata, "VIDEO.HLS.URL")
    if url:
        return url
    doc = await COLLPARAM.find_one(
        {"vehicle_id": vehicle_id},
        {"_id": 0, "vehicle_id": 1, "metadata": 1},
    )
    if doc is None:
        return None
    for data in doc["metadata"]:
        if data["key"] == "VIDEO.HLS.URL":
            await RedisDB.hset(vkey.metadata, "VIDEO.HLS.URL", data["value"])
            return data["value"]
    return None
