<template>
  <div class="vehicle-card">
    <div class="flex justify-end items-center">
      <el-tooltip effect="light" placement="top" content="編集">
        <Icon class="w-6 card-icon" :size="16" name="el-icon-Edit" @click="vehicleEdit" />
      </el-tooltip>
      <el-tooltip effect="light" placement="top" content="削除">
        <Icon class="w-6 card-icon" :size="16" name="el-icon-Delete" @click="vehicleDelete" />
      </el-tooltip>
    </div>
    <div class="mx-auto">
      <img :src="typeImg[vehicle_type]" alt="" class="h-[80px]" />
    </div>
    <div class="vehicle-info">
      <div class="label">重機番号:</div>
      <div class="value">{{ vehicle_name || "--" }}</div>
    </div>
    <!-- <div class="vehicle-info">
      <div class="label">重機番型:</div>
      <div class="value">{{ typeEnum[vehicle_type] || "--" }}</div>
    </div> -->
    <div class="vehicle-info">
      <div class="label">SN番号:</div>
      <div class="value">{{ vehicle_sn || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">所在地:</div>
      <div class="value">{{ vehicle_address || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">取り付け日:</div>
      <div class="value">{{ install_date ? dayjs(install_date).format("YYYY-MM-DD") : "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">引き渡し日:</div>
      <div class="value">
        {{ delivery_date ? dayjs(delivery_date).format("YYYY-MM-DD") : "--" }}
      </div>
    </div>
    <div class="vehicle-info">
      <div class="label">保証終了日:</div>
      <div class="value">
        {{ warranty_end_date ? dayjs(warranty_end_date).format("YYYY-MM-DD") : "--" }}
      </div>
    </div>
    <div class="vehicle-info">
      <div class="label h-[52px]">機能付き:</div>
      <div class="value h-[52px] w-[88px] two-line-ellipsis">
        <el-tooltip effect="light" placement="top" :content="smartFeatureDisplay">
          <span>{{ smartFeatureDisplay }}</span>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import excavatorImg from "@/assets/images/project/wajueji.gif";
import loaderImg from "@/assets/images/project/zhuangzaiji.gif";
import bulldozerImg from "@/assets/images/project/tuituji.gif";
import shovelImg from "@/assets/images/project/dianchan.gif";
import slingerImg from "@/assets/images/project/diaoyunji.gif";

import useMetadataStore from "@/stores/modules/metadata";
const metadataStore = useMetadataStore();

const typeImg: any = {
  1: excavatorImg,
  2: shovelImg,
  4: loaderImg,
  7: bulldozerImg,
  11: slingerImg,
};

const typeEnum: any = {
  1: "ショベル",
  2: "ロープショベル",
  4: "ホイルローダー",
  7: "ブルドーザー",
  11: "リーチスタッカー",
};

const props = defineProps({
  vehicle_id: {
    type: String,
    default: "",
  },
  vehicle_name: {
    type: String,
    default: "",
  },
  vehicle_type: {
    type: Number,
    default: 1,
  },
  vehicle_sn: {
    type: String,
    default: "",
  },
  vehicle_address: {
    type: String,
    default: "",
  },
  install_date: {
    type: String,
    default: "",
  },
  delivery_date: {
    type: String,
    default: "",
  },
  warranty_end_date: {
    type: String,
    default: "",
  },
  smart_feature: {
    type: Array,
    default: () => [],
  },
});

const {
  vehicle_id,
  vehicle_name,
  vehicle_type,
  vehicle_sn,
  vehicle_address,
  install_date,
  delivery_date,
  warranty_end_date,
  smart_feature,
} = toRefs(props);

const emit = defineEmits(["edit", "copy", "delete"]);

const vehicleEdit = () => {
  emit("edit", vehicle_id.value);
};

const vehicleDelete = () => {
  emit("delete", vehicle_id.value);
};

const smartFeatureList: any = ref({});
onMounted(async () => {
  smartFeatureList.value = await metadataStore.fetchMetadata("smart_feature", "kv");
});

const smartFeatureDisplay = computed(() => {
  if (!smart_feature.value || smart_feature.value.length === 0) return '--';
  return smart_feature.value
    .map(item => smartFeatureList.value[item as number])
    .join('、');
});
</script>
<style scoped lang="scss">
img {
  user-select: none; /* 防止用户选择图片 */
  pointer-events: none; /* 防止图片被点击或拖动 */
}

.vehicle-card {
  padding: 8px;
  width: 190px;
  height: 100%;
  background-color: #f4f4f4;
  border-radius: 8px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  .vehicle-info {
    height: 26px;
    line-height: 26px;
    display: flex;
    flex-direction: row;
    .label {
      width: 86px;
      color: #222831;
    }
    .value {
      color: #393e46;
    }
  }
}
.card-icon {
  cursor: pointer;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 4px;
  background-color: #bbbbbb;
  transition: all 0.3s linear;

  &:hover {
    background-color: var(--el-color-primary);
    transform: scale(1.1);
  }
}

.card-icon + .card-icon {
  margin-left: 4px;
}

:deep(.el-progress-bar__outer) {
  background-color: #cccccc;
}
</style>
