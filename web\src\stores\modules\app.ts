import { defineStore } from "pinia";
interface AppSate {
  config: Record<string, any>;
  isMobile: boolean;
  isCollapsed: boolean;
  isRouteShow: boolean;
}

const useAppStore = defineStore({
  id: "app",
  state: (): AppSate => {
    return {
      config: {
        copyright: [
          {
            name: "© 2024 北京拓疆者智能科技有限公司 | 京ICP备18048501号-3",
            link: "http://www.beian.gov.cn",
          },
        ],
        webName: "拓疆者故障诊断系统",
        ossDomain: "http://127.0.0.1:8000",
      },
      isMobile: true,
      isCollapsed: false,
      isRouteShow: true,
    };
  },
  actions: {
    getImageUrl(url: string) {
      return url ? `${this.config.ossDomain}/${url}` : "";
    },
    //TODO处理图片url返回缺少api/uploads
    getimageUrl(url: string) {
      return url ? `${this.config.ossDomain}/api/uploads/${url}/` : "";
    },
    getConfig() {
      return new Promise((resolve, reject) => {
        resolve(this.config);
      });
    },
    setMobile(value: boolean) {
      this.isMobile = value;
    },
    toggleCollapsed(toggle?: boolean) {
      this.isCollapsed = toggle ?? !this.isCollapsed;
    },
    refreshView() {
      this.isRouteShow = false;
      nextTick(() => {
        this.isRouteShow = true;
      });
    },
  },
});

export default useAppStore;
